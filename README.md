# 🚀 JH-Loan-Cash 前端环境配置说明

## 🌍 环境概览

本项目支持以下环境配置：

| 环境 | 描述 | 用途 |
|------|------|------|
| **dev** | 本地开发环境 | 日常开发调试 |
| **pre** | 预发布环境 | 上线前验证 |
| **fat** | 功能测试环境 | 功能测试验证 |
| **uat** | 用户验收测试环境 | 用户验收测试 |
| **prod** | 生产环境 | 正式生产环境 |

---

## ⚡ 启动命令

### 开发环境启动 (Development)
```bash
# 本地开发环境
npm run dev

# 预发布环境
npm run dev:pre

# 功能测试环境
npm run dev:fat

# 用户验收测试环境
npm run dev:uat
```

### 生产构建 (Build)
```bash
# 生产环境构建
npm run build:prod

# 预发布环境构建
npm run build:pre

# 功能测试环境构建
npm run build:fat

# 用户验收测试环境构建
npm run build:uat
```

# ELADMIN-WEB

ELADMIN 前端源码

#### 项目源码

|     |   后端源码  |   前端源码  |
|---  |--- | --- |
|  github   |  https://github.com/elunez/eladmin   |  https://github.com/elunez/eladmin-web   |
|  码云   |  https://gitee.com/elunez/eladmin   |  https://gitee.com/elunez/eladmin-web   |

#### 开发文档
[https://eladmin.vip](https://eladmin.vip)

#### 体验地址
[https://eladmin.vip/demo](https://eladmin.vip/demo)

#### 前端模板

初始模板基于： [https://github.com/PanJiaChen/vue-element-admin](https://github.com/PanJiaChen/vue-element-admin)

模板文档： [https://panjiachen.github.io/vue-element-admin-site/zh/guide/](https://panjiachen.github.io/vue-element-admin-site/zh/guide/)

#### Build Setup
**推荐 node 版本：12-16**
``` bash
# 配置镜像加速
https://www.ydyno.com/archives/1219.html

# 安装依赖
npm install

# 启动服务 localhost:8013
npm run dev

# 构建生产环境
npm run build:prod
```

#### 常见问题

1、linux 系统在安装依赖的时候会出现 node-sass 无法安装的问题

解决方案：
```
1. 单独安装：npm install --unsafe-perm node-sass 
2. 直接使用：npm install --unsafe-perm
```

2、加速node-sass安装

https://www.ydyno.com/archives/1219.html