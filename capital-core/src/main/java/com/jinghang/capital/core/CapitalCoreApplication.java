package com.jinghang.capital.core;

import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.cloud.openfeign.EnableFeignClients;

@SpringBootApplication
@EnableFeignClients(basePackages = { "com.jinghang.capital.core.service.remote", "com.jinghang.capital.core.remote"})
public class CapitalCoreApplication {

    public static void main(String[] args) {
        SpringApplication.run(CapitalCoreApplication.class, args);
    }
}