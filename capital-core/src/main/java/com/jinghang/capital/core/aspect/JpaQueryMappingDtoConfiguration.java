package com.jinghang.capital.core.aspect;


import jakarta.annotation.PostConstruct;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.FatalBeanException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationContext;
import org.springframework.context.annotation.Configuration;
import org.springframework.core.convert.support.DefaultConversionService;
import org.springframework.core.convert.support.GenericConversionService;

import java.beans.PropertyDescriptor;
import java.lang.reflect.Method;
import java.lang.reflect.Modifier;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2023/8/30 13:53
 */
@Configuration
public class JpaQueryMappingDtoConfiguration {

    private static final Logger logger = LoggerFactory.getLogger(JpaQueryMappingDtoConfiguration.class);

    @Autowired
    private ApplicationContext applicationContext;

    /**
     * 初始化注入@JpaDto对应的Converter
     */
    @PostConstruct
    public void init() {
        Map<String, Object> map = applicationContext.getBeansWithAnnotation(JpaQueryMappingDtoAspect.class);
        for (Object o : map.values()) {
            Class c = o.getClass();
            logger.info("Jpa添加Converter,class={}", c.getName());
            GenericConversionService genericConversionService = (GenericConversionService) DefaultConversionService.getSharedInstance();
            genericConversionService.addConverter(Map.class, c, m -> {
                try {
                    Object obj = c.getDeclaredConstructor().newInstance();
                    // 这里可以扩展,注入的converter,实现sql查寻出的结果为数据库中带下划线的字段,通过程序转为驼峰命名再设置到实体中
                    // 也可以做类型转换判断,这里未做类型判断,直接copy到dto中,类型不匹配的时候可能会出错
                    return copyMapToObj(m, obj);
                } catch (Exception e) {
                    throw new FatalBeanException("Jpa结果转换出错,class=" + c.getName(), e);
                }
            });
        }
    }

    /**
     * 将map中的值copy到bean中对应的字段上
     *
     * @param map
     * @param target
     * @return
     * <AUTHOR>
     * @date 2020-03-26
     */
    private Object copyMapToObj(Map<String, Object> map, Object target) {
        if (map == null || target == null || map.isEmpty()) {
            return target;
        }
        Class<?> actualEditable = target.getClass();
        PropertyDescriptor[] targetPds = BeanUtils.getPropertyDescriptors(actualEditable);
        for (PropertyDescriptor targetPd : targetPds) {
            if (targetPd.getWriteMethod() == null) {
                continue;
            }
            try {
                String key = targetPd.getName();
                Object value = map.get(key);
                if (value == null) {
                    continue;
                }
                Method writeMethod = targetPd.getWriteMethod();
                if (!Modifier.isPublic(writeMethod.getDeclaringClass().getModifiers())) {
                    writeMethod.setAccessible(true);
                }
                writeMethod.invoke(target, value);
            } catch (Exception ex) {
                logger.error("Could not copy properties from source to target,{}, {}", ex, targetPd.getName());
                throw new FatalBeanException("Could not copy properties from source to target ", ex);
            }
        }
        return target;
    }

}
