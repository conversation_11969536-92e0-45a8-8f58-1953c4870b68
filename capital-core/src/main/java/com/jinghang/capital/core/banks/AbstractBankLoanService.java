package com.jinghang.capital.core.banks;

import com.jinghang.capital.core.convert.EnumConvert;
import com.jinghang.capital.core.convert.entityvo.VoLoanConvert;
import com.jinghang.capital.core.dto.ContractBizDto;
import com.jinghang.capital.core.dto.QuotaQueryDto;
import com.jinghang.capital.core.dto.QuotaQueryResultDto;
import com.jinghang.capital.core.entity.Loan;
import com.jinghang.capital.core.entity.LoanReplan;
import com.jinghang.capital.core.enums.LoanStage;
import com.jinghang.capital.core.enums.LoanStatus;
import com.jinghang.capital.core.enums.ProcessStatus;
import com.jinghang.capital.core.exception.BizErrorCode;
import com.jinghang.capital.core.exception.BizException;
import com.jinghang.capital.core.service.CommonService;
import com.jinghang.capital.core.service.MqService;
import com.jinghang.capital.core.service.WarningService;
import com.jinghang.capital.core.service.loan.FinLoanService;
import com.jinghang.capital.core.util.AmountUtil;
import com.jinghang.capital.core.vo.BusinessChronosProcessResultVo;
import com.jinghang.capital.core.vo.BusinessChronosProcessVo;
import com.jinghang.capital.core.vo.loan.LoanApplyVo;
import com.jinghang.capital.core.vo.loan.LoanNoticeResultVo;
import com.jinghang.capital.core.vo.loan.LoanNoticeVo;
import com.jinghang.capital.core.vo.loan.LoanQueryVo;
import com.jinghang.capital.core.vo.loan.LoanResultVo;
import com.jinghang.capital.core.vo.loan.LoanTrialQueryVo;
import com.jinghang.capital.core.vo.loan.LoanTrialResultVo;
import com.jinghang.capital.core.vo.loan.OrderCancelResultVo;
import com.jinghang.capital.core.vo.loan.OrderCancelVo;
import com.jinghang.capital.core.vo.repay.PlanItemVo;
import com.jinghang.capital.core.vo.repay.PlanVo;
import com.jinghang.common.loan.PlanGenerator;
import com.jinghang.common.loan.plan.InterestType;
import com.jinghang.common.loan.plan.RepayPlan;
import com.jinghang.common.util.CollectionUtil;
import com.jinghang.common.util.JsonUtil;
import com.jinghang.common.util.StringUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalTime;
import java.time.format.DateTimeFormatter;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

public abstract class AbstractBankLoanService implements BankLoanService {

    private static final Logger logger = LoggerFactory.getLogger(AbstractBankLoanService.class);

    @Autowired
    private CommonService commonService;

    @Autowired
    private MqService mqService;

    @Autowired
    private FinLoanService finLoanService;

    @Autowired
    private WarningService warningService;



    // 时分时间格式
    protected static final DateTimeFormatter FORMATTER_HH_MM = DateTimeFormatter.ofPattern("HHmm");

    @Override
    public LoanResultVo apply(LoanApplyVo apply) {
        // 校验
        // 本地校验
        boolean valid = bankLoanValidate(apply);

        if (!valid) {
            throw new BizException(BizErrorCode.LOAN_INVALID);
        }

        // 落库保存
        Loan loan = commonService.loanApply(apply);

        // 保存订单信息
        saveLoanOrder(apply);

        mqService.submitLoanApply(loan.getId());

        return VoLoanConvert.INSTANCE.toLoanResultDto(loan);
    }

    protected abstract boolean bankLoanValidate(LoanApplyVo apply);

    /**
     * 放款时间校验
     *
     * @param loanStartTime 放款开始时间 HHmm
     * @param loanEndTime   放款结束时间 HHmm
     */
    protected void loanTimeCheck(String loanStartTime, String loanEndTime) {
        LocalTime now = LocalTime.now();
        LocalTime start = LocalTime.parse(loanStartTime, FORMATTER_HH_MM);
        LocalTime end = LocalTime.parse(loanEndTime, FORMATTER_HH_MM);
        if (now.isBefore(start) || now.isAfter(end)) {
            throw new BizException(BizErrorCode.LOAN_TIME_INVALID);
        }
    }

    @Override
    public void saveLoanOrder(LoanApplyVo apply) {

    }

    /**
     * 额度查询结果回调
     *
     * @param dto
     */
    @Override
    public void quotaQueryResultCallback(QuotaQueryDto dto, QuotaQueryResultDto resultDto) {
    }

    /**
     * 调额结果回调
     *
     * @param adjustId
     */
    @Override
    public void quotaAdjustResultCallback(String adjustId) {
    }

    /**
     * 资方申请放款
     *
     * @param loan
     */
    @Override
    public void bankApply(Loan loan) {
        // 调用资方放款
        LoanResultVo resultVo = bankLoanApply(loan);

        // 更新放款结果
        bankLoanApplyResult(loan, resultVo);
    }

    public void bankLoanApplyResult(Loan loan, LoanResultVo resultVo) {
        loan.setLoanStatus(EnumConvert.INSTANCE.toLoanStatus(resultVo.getStatus()));
        loan.setFailMsg(resultVo.getFailMsg());
        loan.setLoanNo(resultVo.getLoanNo());
        finLoanService.updateLoan(loan);

        if (resultVo.getStatus() != ProcessStatus.FAIL) {
            // 查询资方放款结果
            mqService.submitLoanResultQueryDelay(loan.getId());
        }

    }

    /**
     * 内部队列查询
     *
     * @param query
     */
    @Override
    public void query(LoanQueryVo query) {
        Loan loan = commonService.findLoanById(query.getLoanId());
        if (loan.getLoanStatus().isFinal()) {
            return;
        }
        // 查询资方放款结果
        LoanResultVo loanResultVo = bankLoanQuery(loan, query); // 此方法内，不要修改保存loan对象


        if (ProcessStatus.PROCESSING.equals(loanResultVo.getStatus())) {
            mqService.submitLoanResultQueryDelay(loan.getId());
            return;
        }
        // 放款结果
        loan.setLoanNo(StringUtil.isNotBlank(loanResultVo.getLoanNo()) ? loanResultVo.getLoanNo() : loan.getLoanNo());
        loan.setLoanContractNo(StringUtil.isNotBlank(loanResultVo.getLoanContractNo()) ? loanResultVo.getLoanContractNo() : loan.getLoanContractNo());
        loan.setLoanChannel(loanResultVo.getLoanChannel());
        loan.setLoanStatus(ProcessStatus.SUCCESS.equals(loanResultVo.getStatus()) ? LoanStatus.SUCCESS : LoanStatus.FAIL);
        loan.setFailMsg(loanResultVo.getFailMsg());
        loan.setLoanTime(null != loanResultVo.getLoanTime() ? loanResultVo.getLoanTime() : loan.getLoanTime());
        // 出资模式
        loan.setFundingModel(loanResultVo.getFundingModel());
        if (ProcessStatus.SUCCESS.equals(loanResultVo.getStatus())) {

            // 生成还款计划
            logger.info("生成还款计划, loanId: {}", loanResultVo.getLoanId());
            PlanVo planVo = getBankPlan(loan);

            if (CollectionUtil.isEmpty(planVo.getPlanItems()) || !Objects.equals(loan.getPeriods(), planVo.getPlanItems().size())) {
                logger.error("还款计划条数与借款期数不一致，loanId: {}，period:{}, loanRepayPlan size:{}", loan.getId(), loan.getPeriods(),
                    planVo.getPlanItems().size());
                warningService.warn("还款计划条数与借款期数不一致, loanId:" + loan.getId());
                mqService.submitLoanResultQueryDelay(loan.getId());
                return;
            }
            //保存更新到本地还款计划表
            List<LoanReplan> replanList = finLoanService.genRepayPlans(planVo); // save the plan list


        }
        finLoanService.updateLoan(loan);
    }

    @Override
    public void generatePlanFeeList(List<LoanReplan> plans, PlanVo planVo) {

    }

    /**
     * 借款试算
     *
     * @param loanTrialQueryVo
     * @return
     */
    @Override
    public LoanTrialResultVo loanTrial(LoanTrialQueryVo loanTrialQueryVo) {
        return null;
    }

    /**
     * 放款通知
     *
     * @param loanNoticeVo
     * @return
     */
    @Override
    public LoanNoticeResultVo loanNotice(LoanNoticeVo loanNoticeVo) {
        return null;
    }

    @Override
    public BusinessChronosProcessResultVo loanContractNoQuery(BusinessChronosProcessVo loanNoticeVo) {
        return null;
    }

    /**
     * 调用资方放款申请
     *
     * @param loan 放款
     */
    protected abstract LoanResultVo bankLoanApply(Loan loan);


    /**
     * 获取还款计划成功后业务处理
     *
     * @param loan 借款
     */
    public void afterLoanPlanSuccess(Loan loan) {
    }

    /**
     * 调用资方放款查询
     *
     * @param loan 放款
     */
    protected abstract LoanResultVo bankLoanQuery(Loan loan, LoanQueryVo query);


    protected abstract PlanVo getBankPlan(Loan loan);


    public void contractDownload(String loanId) {
    }

    public void contractUpload(String loanId) {
    }

    @Override
    public void bankSignQuery(String loanId) {

    }

    protected MqService getMqService() {
        return mqService;
    }

    protected CommonService getCommonService() {
        return commonService;
    }

    protected FinLoanService getFinLoanService() {
        return finLoanService;
    }

    protected WarningService getWarningService() {
        return warningService;
    }

    public void fileUploadNotify(String id) {

    }

    @Override
    public void loanAfterAgreementSign(Loan loan) {
        // empty
    }

    @Override
    public void usingLetterQuery(String loanId) {

    }

    @Override
    public LoanResultVo loanQuery(Loan loan) {
        return VoLoanConvert.INSTANCE.toLoanResultDto(loan);
    }
    /**
     * 订单撤销
     * @param vo
     * @return
     */
    @Override
    public OrderCancelResultVo orderCancel(OrderCancelVo vo) {
        return null;
    }
}
