package com.jinghang.capital.core.banks;


import com.jinghang.capital.core.dto.SignDynamicParamDto;
import com.jinghang.capital.core.dto.SignTemplateParamDto;
import com.jinghang.capital.core.entity.AgreementSignature;
import com.jinghang.capital.core.enums.AgreementType;
import com.jinghang.capital.core.service.ChannelSupport;
import com.jinghang.capital.core.service.remote.nfsp.sign.req.SignApplyReq;
import com.jinghang.capital.core.service.remote.nfsp.sign.res.ResultMsg;

/**
 * 合同信息相关服务
 */
public interface BankContractInfoService extends ChannelSupport {
    SignApplyReq fetchDynamicSignParam(String businessId, AgreementType agreementType);

    SignApplyReq fetchTemplateSignParam(String businessId, AgreementSignature agreementSignature);

}
