package com.jinghang.capital.core.banks;




import com.jinghang.capital.core.dto.AdjustLimitBizDto;
import com.jinghang.capital.core.dto.QuotaQueryDto;
import com.jinghang.capital.core.dto.QuotaQueryResultDto;
import com.jinghang.capital.core.entity.Credit;
import com.jinghang.capital.core.entity.FailedCredit;
import com.jinghang.capital.core.service.ChannelSupport;
import com.jinghang.capital.core.vo.credit.CreditApplyVo;
import com.jinghang.capital.core.vo.credit.CreditQueryVo;
import com.jinghang.capital.core.vo.credit.CreditResultVo;
import com.jinghang.capital.core.vo.credit.ExtInfoVo;
import com.jinghang.capital.core.vo.credit.PreCreditApplyResultVo;
import com.jinghang.capital.core.vo.credit.PreCreditApplyVo;
import com.jinghang.capital.core.vo.credit.RecreditApplyVo;


import java.math.BigDecimal;

public interface BankCreditService extends ChannelSupport {

    ////授信
    //1，授信验证入库
    CreditResultVo apply(CreditApplyVo<ExtInfoVo> apply);
    //2, mq任务去上传协议影像内容
    void bankApply(Credit credit);
    //3,mq,调用资方授信
    void contractUpload(String creditId);
    //4,授信结果查询（通过）
    void query(CreditQueryVo apply);



    /**
     * 风控失败通知授信
     *
     * @param apply
     * @return
     */
    CreditResultVo failedNotify(CreditApplyVo<ExtInfoVo> apply);

    /**
     * 风控失败记录推送资方
     *
     * @param failedCredit 失败授信记录
     */
    void bankFailedNotify(FailedCredit failedCredit);

    /**
     * 设定资方利率
     *
     * @return 利率
     */
    BigDecimal getBankRate();


    void contractDownload(String creditId);

    void bankSignQuery(String creditId);

    CreditResultVo recreditApply(RecreditApplyVo recreditApply);

    /**
     * 重新签署授信阶段协议
     *
     * @param credit 授信记录
     */
    void creditAgreementRecreate(Credit credit);

    /**
     * 预授信申请 撞库
     *
     * @param preCreditApplyVo
     * @return
     */
    PreCreditApplyResultVo preApply(PreCreditApplyVo preCreditApplyVo);

    /**
     * 调额查询方法
     *
     * @param bizDto
     */
    void adjustLimitQuery(AdjustLimitBizDto bizDto);

    /**
     * 额度查询结果回调
     * @param dto
     */
    void quotaQueryResultCallback(QuotaQueryDto dto, QuotaQueryResultDto resultDto);

    /**
     * 调额结果回调
     * @param adjustId
     */
    void quotaAdjustResultCallback(String adjustId);

    /**
     * 用户信息更新申请
     * @param credit
     */
    void userInfoUpdateApply(Credit credit);

    /**
     * 用户信息更新申请结果查询
     *
     * @param credit
     */
    void userInfoUpdateQuery(Credit credit);


}
