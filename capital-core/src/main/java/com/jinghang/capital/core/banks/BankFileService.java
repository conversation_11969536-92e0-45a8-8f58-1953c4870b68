package com.jinghang.capital.core.banks;




import com.jinghang.capital.api.dto.repay.RepayReturnUploadResultDto;
import com.jinghang.capital.core.dto.CovenantDownApplyDto;
import com.jinghang.capital.core.entity.Loan;
import com.jinghang.capital.core.enums.FileType;
import com.jinghang.capital.core.service.ChannelSupport;
import com.jinghang.capital.core.vo.file.FileDailyProcessVo;
import com.jinghang.capital.core.vo.file.FileDownloadRequestVo;
import com.jinghang.capital.core.vo.file.FileDownloadResultVo;
import com.jinghang.capital.core.vo.file.FileDownloadVo;
import com.jinghang.capital.core.vo.file.FilePushVo;
import com.jinghang.capital.core.vo.file.FileSynRepayPlanResultDVo;
import com.jinghang.capital.core.vo.file.FileSynRepayPlanVo;
import com.jinghang.capital.core.vo.file.FileSyncDueFileResultVo;
import com.jinghang.capital.core.vo.file.FileSyncDueFileVo;
import com.jinghang.capital.core.vo.file.FileUploadResultVo;
import com.jinghang.capital.core.vo.file.FileUploadVo;
import com.jinghang.capital.core.vo.file.PreviewResultVo;
import com.jinghang.capital.core.vo.file.PreviewVo;
import com.jinghang.capital.core.vo.repay.RepayReturnUploadVo;

import java.time.LocalDate;

/**
 * <AUTHOR>
 * @date 2023/5/13
 */
public interface BankFileService extends ChannelSupport {

    FileUploadResultVo upload(FileUploadVo uploadVo);

    /**
     * 下载对应文件
     *
     * @param downloadVo 下载对象dto
     */
    FileDownloadResultVo download(FileDownloadVo downloadVo);

    void processDaily(FileDailyProcessVo processVo);

    void batchVoucherDownload(FileDailyProcessVo processVo);

    void downloadRequest(FileDownloadRequestVo requestVo);

    /**
     * 协议预览
     *
     * @param previewVo
     * @return
     */
    PreviewResultVo preview(PreviewVo previewVo);

    /**
     * 文件推送
     *
     * @param pushVo
     */
    void filePush(FilePushVo pushVo);

    void loanUploadImage(FileUploadVo uploadVo);

    FileSynRepayPlanResultDVo synRepayPlanFile(FileSynRepayPlanVo fileSynRepayPlanVo);

    /**
     * 放款后，下载借款合同
     *
     * @param loan 借据
     */
    void covenantDownloadLoanContract(Loan loan);

    /**
     * 放款后，下载借款凭证
     *
     * @param loan 借据
     */
    void covenantDownloadLoanVoucher(Loan loan);

    /**
     * 放款后，下载协议文件（除借款合同、借款凭证外所有文件）
     *
     * @param loan 借据
     */
    void covenantDownloadOther(Loan loan);

    /**
     * 放款后，下载协议文件（特殊类型--以日期为单位整体下载）
     *
     * @param localDate 业务发生日期
     * @param fileType 文件类型
     */
    void covenantDownloadAgenda(LocalDate localDate, FileType fileType);

    /**
     * 下载需申请下载的协议文件--申请
     *
     * @param dto 下载申请dto
     */
    void covenantDownloadAsyncApply(CovenantDownApplyDto dto);

    /**
     * 下载需申请下载的协议文件--查询申请结果
     *
     * @param applyId 下载申请表的id
     */
    void covenantDownloadAsyncQuery(String applyId);

    FileSyncDueFileResultVo syncDueFile(FileSyncDueFileVo fileSyncDueFileVo);


    RepayReturnUploadResultDto offlineRepayReturnFileUpload(RepayReturnUploadVo applyVo);


}
