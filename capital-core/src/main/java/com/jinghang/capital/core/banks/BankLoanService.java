package com.jinghang.capital.core.banks;



import com.jinghang.capital.core.dto.QuotaQueryDto;
import com.jinghang.capital.core.dto.QuotaQueryResultDto;
import com.jinghang.capital.core.entity.Credit;
import com.jinghang.capital.core.entity.Loan;
import com.jinghang.capital.core.entity.LoanReplan;
import com.jinghang.capital.core.service.ChannelSupport;
import com.jinghang.capital.core.vo.BusinessChronosProcessResultVo;
import com.jinghang.capital.core.vo.BusinessChronosProcessVo;
import com.jinghang.capital.core.vo.loan.LoanApplyVo;
import com.jinghang.capital.core.vo.loan.LoanLprResultVo;
import com.jinghang.capital.core.vo.loan.LoanNoticeResultVo;
import com.jinghang.capital.core.vo.loan.LoanNoticeVo;
import com.jinghang.capital.core.vo.loan.LoanQueryVo;
import com.jinghang.capital.core.vo.loan.LoanResultVo;
import com.jinghang.capital.core.vo.loan.LoanTrialQueryVo;
import com.jinghang.capital.core.vo.loan.LoanTrialResultVo;
import com.jinghang.capital.core.vo.loan.OrderCancelResultVo;
import com.jinghang.capital.core.vo.loan.OrderCancelVo;
import com.jinghang.capital.core.vo.repay.PlanVo;
import com.jinghang.capital.core.vo.repay.RepayDateApplyVo;
import com.jinghang.capital.core.vo.repay.RepayDateResultVo;

import java.util.List;

public interface BankLoanService extends ChannelSupport {

    ////借款
    //1，放款数据组装
    LoanResultVo apply(LoanApplyVo apply);
    //2, mq任务去上传协议影像内容
    void bankApply(Loan loan);
    //3，放款申请
    void contractUpload(String loanId);
    //4，放款申请结果查询和还款计划查询
    void query(LoanQueryVo query);




    void generatePlanFeeList(List<LoanReplan> plans, PlanVo planVo);

    RepayDateResultVo appropriationInterest(RepayDateApplyVo applyVo);

    LoanLprResultVo lpr(Credit credit);

    LoanTrialResultVo loanTrial(LoanTrialQueryVo loanTrialQueryVo);

    LoanNoticeResultVo loanNotice(LoanNoticeVo loanNoticeVo);

    BusinessChronosProcessResultVo loanContractNoQuery(BusinessChronosProcessVo loanNoticeVo);

    void contractDownload(String loanId);
    
    void bankSignQuery(String loanId);

    void fileUploadNotify(String creditId);

    void loanAfterAgreementSign(Loan loan);

    /**
     * 获取还款计划成功后业务处理
     * @param loan 借款
     */
    void afterLoanPlanSuccess(Loan loan);

    void saveLoanOrder(LoanApplyVo apply);

    /**
     * 额度查询结果回调
     *
     * @param dto
     */
    void quotaQueryResultCallback(QuotaQueryDto dto, QuotaQueryResultDto resultDto);

    /**
     * 调额结果回调
     *
     * @param adjustId
     */
    void quotaAdjustResultCallback(String adjustId);

    void usingLetterQuery(String loanId);

    LoanResultVo loanQuery(Loan loan);

    OrderCancelResultVo orderCancel(OrderCancelVo vo);
}
