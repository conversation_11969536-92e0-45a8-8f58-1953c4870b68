package com.jinghang.capital.core.banks;


import com.jinghang.capital.core.dto.BankProcessDTO;
import com.jinghang.capital.core.service.ChannelSupport;
import com.jinghang.capital.core.vo.repay.PlanSyncVo;
import com.jinghang.capital.core.vo.repay.PlanVo;

/**
 * <AUTHOR>
 * @date 2023/5/17
 */
public interface BankPlanService extends ChannelSupport {
    /**
     *  wld 使用
     * @param planSyncVo
     */
    void planSync(PlanSyncVo planSyncVo);

    /**
     * wld 同步数据
     * @param recId
     */
    void planPush(String recId);

    /**
     * 推送计划详情到core
     * @param planVo 还款计划
     */
    void planDetailPush(PlanVo planVo);

    /**
     * 发送具体的还款计划给资方
     * @param loanId 借据
     */
    void syncPlanToCapital(String loanId);


    void syncRePlanManual(BankProcessDTO bankProcessDTO);
}
