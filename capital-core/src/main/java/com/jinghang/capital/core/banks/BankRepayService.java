package com.jinghang.capital.core.banks;




import com.jinghang.capital.core.dto.BankProcessDTO;
import com.jinghang.capital.core.entity.BankBatchSubstituteRecord;
import com.jinghang.capital.core.entity.BankRepayRecord;
import com.jinghang.capital.core.entity.BatchCustomerRepayRecord;
import com.jinghang.capital.core.entity.CustomerRepayRecord;
import com.jinghang.capital.core.entity.DefrayRecord;
import com.jinghang.capital.core.entity.FlowClaimRecord;
import com.jinghang.capital.core.entity.Loan;
import com.jinghang.capital.core.service.ChannelSupport;

import com.jinghang.capital.core.vo.recc.RccConfirmVo;
import com.jinghang.capital.core.vo.recc.ReccDetailQueryResultVo;
import com.jinghang.capital.core.vo.recc.ReccDetailQueryVo;
import com.jinghang.capital.core.vo.recc.ReccResultVo;
import com.jinghang.capital.core.vo.repay.ActiveClaimApplyResultVo;
import com.jinghang.capital.core.vo.repay.ActiveClaimApplyVo;
import com.jinghang.capital.core.vo.repay.ActiveClaimQueryResultVo;
import com.jinghang.capital.core.vo.repay.ActiveClaimQueryVo;
import com.jinghang.capital.core.vo.repay.ActiveLaunchClaimApplyVo;
import com.jinghang.capital.core.vo.repay.ActiveLaunchClaimResultVo;
import com.jinghang.capital.core.vo.repay.BankPlanQueryRltVo;
import com.jinghang.capital.core.vo.repay.BankPlanQueryVo;
import com.jinghang.capital.core.vo.repay.BankRepayRecordVo;
import com.jinghang.capital.core.vo.repay.BatchTrailVo;
import com.jinghang.capital.core.vo.repay.BatchTrialResultVo;
import com.jinghang.capital.core.vo.repay.ClaimMarkApplyVo;
import com.jinghang.capital.core.vo.repay.ClaimMarkResultVo;
import com.jinghang.capital.core.vo.repay.ClaimRetryResultVo;
import com.jinghang.capital.core.vo.repay.ClaimRetryVo;
import com.jinghang.capital.core.vo.repay.CompensatedRepaySyncRlt;
import com.jinghang.capital.core.vo.repay.CompensatedRepaySyncVo;
import com.jinghang.capital.core.vo.repay.DefrayResultVo;
import com.jinghang.capital.core.vo.repay.DefrayVo;
import com.jinghang.capital.core.vo.repay.FpdDetectionApplyVo;
import com.jinghang.capital.core.vo.repay.FpdDetectionResultVo;
import com.jinghang.capital.core.vo.repay.OverduePlanQueryVo;
import com.jinghang.capital.core.vo.repay.OverduePlanVo;
import com.jinghang.capital.core.vo.repay.PlanOverdueVo;
import com.jinghang.capital.core.vo.repay.PlanQueryVo;
import com.jinghang.capital.core.vo.repay.PlanVo;
import com.jinghang.capital.core.vo.repay.RepayApplyVo;
import com.jinghang.capital.core.vo.repay.RepayDeductionApplyVo;
import com.jinghang.capital.core.vo.repay.RepayNoticeResultVo;
import com.jinghang.capital.core.vo.repay.RepayNoticeVo;
import com.jinghang.capital.core.vo.repay.RepayResultVo;
import com.jinghang.capital.core.vo.repay.RepayTrailVo;
import com.jinghang.capital.core.vo.repay.SubstituteApplyResultVo;
import com.jinghang.capital.core.vo.repay.SubstituteApplyVo;
import com.jinghang.capital.core.vo.repay.SubstituteMarkApplyVo;
import com.jinghang.capital.core.vo.repay.SubstituteMarkResultVo;
import com.jinghang.capital.core.vo.repay.TrailResultVo;
import com.jinghang.capital.core.vo.repay.TrustPlanReleaseVo;

import java.util.List;

public interface BankRepayService extends ChannelSupport {

    ////还款
    //还款试算(当前，结清还款)
    TrailResultVo trail(RepayTrailVo repayTrailVo);
    //还款申请
    RepayResultVo apply(CustomerRepayRecord customerRepayRecord, RepayApplyVo repayApplyDto);
    /**
     * 查询资方还款结果
     * @param repayId bank_repay_record表id
     */
    void query(String repayId);



    PlanVo queryRepayPlan(PlanQueryVo planQueryVo);

    OverduePlanVo queryOverduePlan(OverduePlanQueryVo planQueryVo);

    CompensatedRepaySyncRlt compensatedRepaySync(CompensatedRepaySyncVo syncVo);

    ClaimMarkResultVo claimMark(ClaimMarkApplyVo applyVo);

    ClaimRetryResultVo claimRetry(ClaimRetryVo claimRetryVo);

    RepayNoticeResultVo repayNotice(RepayNoticeVo repayNoticeVo);

    ActiveLaunchClaimResultVo activeLaunchClaim(ActiveLaunchClaimApplyVo vo);

    ActiveClaimApplyResultVo activeClaimApply(ActiveClaimApplyVo vo);

    ActiveClaimQueryResultVo activeClaimQuery(ActiveClaimQueryVo vo);

    boolean repayValidate(RepayApplyVo repayApplyVo);

    void claimApply(String bankLoanReplanId);

    void claimQuery(String bankRepayRecordId);


    void flowClaimApply(FlowClaimRecord flowClaimRecord);

    void flowClaimQuery(FlowClaimRecord flowClaimRecord);


    void claimAfterNotify(String recordId);

    /**
     * 对资还款消息入口
     *
     * @param bankRepayRecordId 对资还款记录表
     */
    void bankRepayApplyMq(String bankRepayRecordId);

    /**planQuery
     * 通知资方还款（对客已还）
     *
     * @param customerRepayRecord
     */
    void bankRepayNotify(CustomerRepayRecord customerRepayRecord);

    /**
     * 批量通知资方（对客已还）
     *
     * @param processDTO
     */
    default void bankRepayBatchNotify(BankProcessDTO processDTO) {
    }

    /**
     * 还款重试
     *
     * @param customerRepayRecord
     */
    void repayCustomRetry(CustomerRepayRecord customerRepayRecord);


    RepayResultVo batchApply(String batchRepayId);

    boolean batchApplyRepayValidate(RepayDeductionApplyVo repayApplyVo);

    RepayResultVo batchGroupRepayApply(BatchCustomerRepayRecord repayRecord);

    void batchRepayApply(BatchCustomerRepayRecord repayRecord);

    void batchRepayQuery(String repayId);


    BatchTrialResultVo batchTrial(BatchTrailVo trailVo);

    void planOverdue(PlanOverdueVo planOverdueVo);

    void trustPlanRelease(TrustPlanReleaseVo trustPlanReleaseVo);

    DefrayResultVo defray(DefrayVo defrayVo);

    SubstituteMarkResultVo substituteMark(SubstituteMarkApplyVo substituteMarkApplyVo);

    SubstituteApplyResultVo substituteApply(SubstituteApplyVo substituteApplyVo);

    SubstituteApplyResultVo handSubstituteApply(SubstituteApplyVo substituteApplyVo);

    void bankSubstituteApply(BankBatchSubstituteRecord bankBatchSubstituteRecord);

    BankRepayRecord populateBankRepayRecord(TrailResultVo trailResult, BankRepayRecord bankRepayRecord);

    BatchTrialResultVo buildBatchCustomerTrialResult(Loan loan, RepayDeductionApplyVo repayApplyVo);

    List<BankRepayRecordVo> queryBankRepayRecord(String loanNo, BatchCustomerRepayRecord repayRecord);

    BankPlanQueryRltVo bankPlanQuery(BankPlanQueryVo planQueryVo);

    ReccResultVo confirm(RccConfirmVo vo);

    ReccDetailQueryResultVo queryRccDetail(ReccDetailQueryVo vo);



}
