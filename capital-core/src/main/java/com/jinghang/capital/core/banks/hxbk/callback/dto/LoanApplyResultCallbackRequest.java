package com.jinghang.capital.core.banks.hxbk.callback.dto;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.jinghang.capital.core.banks.hxbk.dto.loan.ReceiptInfoBean;

import java.util.List;

/**
 * HXBK 用信结果回调请求数据
 * 根据接口文档定义的请求参数结构
 *
 * @Author: zhaohaojie
 * @CreateTime: 2025/7/10
 */
@JsonInclude(JsonInclude.Include.NON_EMPTY)
public class LoanApplyResultCallbackRequest {

    /**
     * 用信申请的订单号
     * prod_type=1时，用信申请的订单号；
     */
    @JsonProperty("order_no")
    private String orderNo;

    /**
     * 业务类型
     * 1：现金贷 2：分期付
     */
    @JsonProperty("prod_type")
    private String prodType;

    /**
     * 资产方用户唯一标识
     */
    @JsonProperty("open_id")
    private String openId;

    /**
     * 资产方购物订单号
     * 非必传
     * 购物订单号，如二轮车/摩托车订单号
     */
    @JsonProperty("biz_order_no")
    private String bizOrderNo;

    /**
     * 授信状态
     * 0-通过 1-不通过 2-处理中
     */
    @JsonProperty("status")
    private String status;

    /**
     * 拒绝原因
     */
    @JsonProperty("msg")
    private String msg;

    /**
     * 借据信息
     * 非必传
     * 成功时返回
     */
    @JsonProperty("receipt_info")
    private ReceiptInfoBean receiptInfo;

    /**
     * 还款计划列表
     * 非必传
     * 成功时返回
     */
    @JsonProperty("repay_ref")
    private List<HXBKRepayRefVO> repayRef;

    /**
     * 用信合同路径
     * 非必传
     * 配合文件下载接口使用
     */
    @JsonProperty("contract_path")
    private String contractPath;

    /**
     * 用信合同号
     * 非必传
     */
    @JsonProperty("contract_no")
    private String contractNo;

    public String getOrderNo() {
        return orderNo;
    }

    public void setOrderNo(String orderNo) {
        this.orderNo = orderNo;
    }

    public String getProdType() {
        return prodType;
    }

    public void setProdType(String prodType) {
        this.prodType = prodType;
    }

    public String getOpenId() {
        return openId;
    }

    public void setOpenId(String openId) {
        this.openId = openId;
    }

    public String getBizOrderNo() {
        return bizOrderNo;
    }

    public void setBizOrderNo(String bizOrderNo) {
        this.bizOrderNo = bizOrderNo;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public String getMsg() {
        return msg;
    }

    public void setMsg(String msg) {
        this.msg = msg;
    }

    public ReceiptInfoBean getReceiptInfo() {
        return receiptInfo;
    }

    public void setReceiptInfo(ReceiptInfoBean receiptInfo) {
        this.receiptInfo = receiptInfo;
    }

    public List<HXBKRepayRefVO> getRepayRef() {
        return repayRef;
    }

    public void setRepayRef(List<HXBKRepayRefVO> repayRef) {
        this.repayRef = repayRef;
    }

    public String getContractPath() {
        return contractPath;
    }

    public void setContractPath(String contractPath) {
        this.contractPath = contractPath;
    }

    public String getContractNo() {
        return contractNo;
    }

    public void setContractNo(String contractNo) {
        this.contractNo = contractNo;
    }
}
