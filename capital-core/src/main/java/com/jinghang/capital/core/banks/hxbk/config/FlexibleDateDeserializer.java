package com.jinghang.capital.core.banks.hxbk.config;

import com.fasterxml.jackson.core.JsonParser;
import com.fasterxml.jackson.databind.DeserializationContext;
import com.fasterxml.jackson.databind.JsonDeserializer;

import java.io.IOException;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.TimeZone;

/**
 * 灵活的Date反序列化器，支持多种日期格式
 * 支持的格式包括：
 * 1. 时间戳格式：**********517 (毫秒级) 或 ********** (秒级)
 * 2. ISO 8601格式：2025-07-29T00:00:00
 * 3. 标准格式：2025-07-29 00:00:00
 * 4. 日期格式：2025-07-29
 *
 * <AUTHOR>
 * @date 2025-07-29
 */
public class FlexibleDateDeserializer extends JsonDeserializer<Date> {
    
    // 支持的日期格式（按优先级排序）
    private static final String[] DATE_PATTERNS = {
        "yyyy-MM-dd'T'HH:mm:ss",    // ISO 8601格式：2025-07-29T00:00:00
        "yyyy-MM-dd HH:mm:ss",      // 标准格式：2025-07-29 00:00:00
        "yyyy-MM-dd"                // 日期格式：2025-07-29
    };
    
    @Override
    public Date deserialize(JsonParser p, DeserializationContext ctxt) throws IOException {
        // 首先检查是否为数字类型（时间戳）
        if (p.getCurrentToken().isNumeric()) {
            try {
                long timestamp = p.getValueAsLong();
                // 判断是秒级还是毫秒级时间戳
                // 如果小于等于10位数字，认为是秒级时间戳
                if (timestamp <= 9999999999L) {
                    return new Date(timestamp * 1000);
                } else {
                    // 毫秒级时间戳
                    return new Date(timestamp);
                }
            } catch (Exception e) {
                throw new IOException("Failed to parse timestamp: " + p.getValueAsString(), e);
            }
        }

        // 处理字符串格式的日期
        String value = p.getValueAsString();
        if (value == null || value.trim().isEmpty()) {
            return null;
        }

        // 检查是否为纯数字字符串（时间戳）
        if (value.matches("\\d+")) {
            try {
                long timestamp = Long.parseLong(value);
                // 判断是秒级还是毫秒级时间戳
                if (timestamp <= 9999999999L) {
                    return new Date(timestamp * 1000);
                } else {
                    return new Date(timestamp);
                }
            } catch (NumberFormatException e) {
                // 继续尝试日期格式解析
            }
        }

        // 尝试各种日期格式进行解析
        for (String pattern : DATE_PATTERNS) {
            try {
                SimpleDateFormat sdf = new SimpleDateFormat(pattern);
                sdf.setTimeZone(TimeZone.getTimeZone("GMT+8"));
                return sdf.parse(value);
            } catch (ParseException e) {
                // 继续尝试下一个格式
            }
        }

        // 所有格式都失败，抛出异常
        throw new IOException("Failed to parse date: " + value +
            ". Supported formats: " + String.join(", ", DATE_PATTERNS) + ", timestamp (seconds/milliseconds)");
    }
}
