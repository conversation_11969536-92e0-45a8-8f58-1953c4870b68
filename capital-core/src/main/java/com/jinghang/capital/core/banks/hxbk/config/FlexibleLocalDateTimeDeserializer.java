package com.jinghang.capital.core.banks.hxbk.config;

import com.fasterxml.jackson.core.JsonParser;
import com.fasterxml.jackson.databind.DeserializationContext;
import com.fasterxml.jackson.databind.JsonDeserializer;

import java.io.IOException;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.time.format.DateTimeParseException;

/**
 * <AUTHOR>
 * @date 2025-07-19 17:47
 */
public class FlexibleLocalDateTimeDeserializer extends JsonDeserializer<LocalDateTime> {
    private static final DateTimeFormatter ISO_FORMAT = DateTimeFormatter.ofPattern("yyyy-MM-dd'T'HH:mm:ss");
    private static final DateTimeFormatter SPACE_FORMAT = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");

    @Override
    public LocalDateTime deserialize(JsonParser p, DeserializationContext ctxt) throws IOException {
        String value = p.getValueAsString();
        try {
            return LocalDateTime.parse(value, ISO_FORMAT);
        } catch (DateTimeParseException e1) {
            try {
                return LocalDateTime.parse(value, SPACE_FORMAT);
            } catch (DateTimeParseException e2) {
                throw new IOException("Failed to parse date: " + value);
            }
        }
    }
}

