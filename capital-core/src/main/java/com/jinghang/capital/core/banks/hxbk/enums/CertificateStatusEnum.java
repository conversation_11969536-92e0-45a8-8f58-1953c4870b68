package com.jinghang.capital.core.banks.hxbk.enums;

/**
 * HXBK结清证明状态枚举
 */
public enum CertificateStatusEnum {
    
    HAS_CERTIFICATE("0", "有结清证明"),
    NO_CERTIFICATE("1", "无结清证明"),
    IN_PROGRESS("2", "开具中"),
    NOT_SUPPORTED("3", "暂不支持开具");

    private final String code;
    private final String desc;

    CertificateStatusEnum(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public String getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }

    /**
     * 根据代码获取枚举
     */
    public static CertificateStatusEnum getByCode(String code) {
        for (CertificateStatusEnum status : values()) {
            if (status.getCode().equals(code)) {
                return status;
            }
        }
        return null;
    }
}
