package com.jinghang.capital.core.banks.hxbk.enums;

import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;

/**
 * HXBK回调接口响应码枚举
 * 符合蚂蚁天枢系统回调接口文档规范
 *
 * @Author: Lior
 * @CreateTime: 2025/7/9 
 */
public enum HXBKCallbackResponseCode {

    /**
     * 请求成功
     */
    SUCCESS("000000", "请求成功"),

    /**
     * 请求参数错误
     */
    PARAM_ERROR("100000", "请求参数错误"),

    /**
     * 系统异常 (200000~299999)
     */
    SYSTEM_ERROR("200000", "系统异常"),

    /**
     * 业务异常 (300000+)
     */
    BUSINESS_ERROR("300000", "业务异常");

    private final String code;
    private final String msg;

    HXBKCallbackResponseCode(String code, String msg) {
        this.code = code;
        this.msg = msg;
    }

    public String getCode() {
        return code;
    }

    public String getMsg() {
        return msg;
    }

    /**
     * 根据异常类型获取对应的响应码
     * 错误只分系统异常和业务异常：
     * - exception为空或message为空：返回系统异常
     * - message不为空：返回业务异常
     *
     * @param exception 异常对象
     * @return 响应码枚举
     */
    public static HXBKCallbackResponseCode fromException(Exception exception) {
        if (ObjectUtil.isNull(exception) || StrUtil.isBlank(exception.getMessage())) {
            return SYSTEM_ERROR;
        }

        // message不为空，返回业务异常
        return BUSINESS_ERROR;
    }
}
