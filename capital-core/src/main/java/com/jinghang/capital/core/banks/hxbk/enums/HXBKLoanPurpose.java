package com.jinghang.capital.core.banks.hxbk.enums;

import com.jinghang.capital.api.dto.LoanPurpose;

import java.util.Arrays;

/**
 * HXBK借款原因枚举
 * 码值映射关系：
 * 01（购物）→ 9
 * 02（医疗健康）→ 8
 * 03（旅游度假）→ 2
 * 04（婚庆）→ 5
 * 05（店面装修）→ 3
 * 06（其他）→ 9
 * 07（教育培训）→ 4
 *
 * @Author: Lior
 * @CreateTime: 2025/7/10 16:00
 */
public enum HXBKLoanPurpose {
    SHOPPING("9", "购物", LoanPurpose.SHOPPING),
    HEALTH("8", "医疗健康", LoanPurpose.HEALTH),
    TOUR("2", "旅游度假", LoanPurpose.TOUR),
    MARRIAGE("5", "婚庆", LoanPurpose.MARRIAGE),
    DECORATION("3", "店面装修", LoanPurpose.DECORATION),
    EDUCATION("4", "教育培训", LoanPurpose.EDUCATION),
    OTHER("9", "其他", LoanPurpose.OTHER);

    private final String code;
    private final String desc;
    private final LoanPurpose loanPurpose;

    HXBKLoanPurpose(String code, String desc, LoanPurpose loanPurpose) {
        this.code = code;
        this.desc = desc;
        this.loanPurpose = loanPurpose;
    }
    /**
     * 根据借款原因枚举获取HXBK借款原因枚举
     *
     * @param loanPurpose 通用借款原因枚举
     * @return HXBK借款原因枚举
     */
    public static HXBKLoanPurpose getCodeEnumByLoanPurpose(LoanPurpose loanPurpose) {
        return Arrays.stream(values()).
                filter(hXBKLoanPurpose -> loanPurpose.equals(hXBKLoanPurpose.loanPurpose))
                .findFirst()
                .orElse(OTHER);
    }


    /**
     * 根据通用借款原因枚举获取HXBK借款原因枚举
     *
     * @param loanPurpose 通用借款原因枚举
     * @return HXBK借款原因枚举
     */
    public static HXBKLoanPurpose getEnumByLoanPurpose(LoanPurpose loanPurpose) {
        return Arrays.stream(values())
                .filter(hxbkLoanPurpose -> loanPurpose.equals(hxbkLoanPurpose.loanPurpose))
                .findFirst()
                .orElse(OTHER);
    }

    /**
     * 根据HXBK编码获取枚举
     *
     * @param code HXBK借款原因编码
     * @return HXBK借款原因枚举
     */
    public static HXBKLoanPurpose getEnumByCode(String code) {
        return Arrays.stream(values())
                .filter(hxbkLoanPurpose -> code.equals(hxbkLoanPurpose.code))
                .findFirst()
                .orElse(OTHER);
    }

    /**
     * 根据通用借款原因枚举获取HXBK借款原因编码
     *
     * @param loanPurpose 通用借款原因枚举
     * @return HXBK借款原因编码
     */
    public static String getCodeByLoanPurpose(LoanPurpose loanPurpose) {
        return getEnumByLoanPurpose(loanPurpose).getCode();
    }

    public String getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }

    public LoanPurpose getLoanPurpose() {
        return loanPurpose;
    }
}
