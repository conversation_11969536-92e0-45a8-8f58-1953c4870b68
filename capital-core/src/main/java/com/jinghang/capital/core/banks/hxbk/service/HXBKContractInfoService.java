package com.jinghang.capital.core.banks.hxbk.service;


import com.jinghang.capital.core.banks.AbstractBankContractInfoService;
import com.jinghang.capital.core.entity.*;
import com.jinghang.capital.core.enums.AgreementType;
import com.jinghang.capital.core.enums.BankChannel;
import com.jinghang.capital.core.repository.AccountBankCardRepository;
import com.jinghang.capital.core.repository.AccountRepository;
import com.jinghang.capital.core.repository.CreditRepository;
import com.jinghang.capital.core.repository.LoanRepository;
import com.jinghang.capital.core.service.remote.nfsp.sign.req.AuthDto;
import com.jinghang.capital.core.service.remote.nfsp.sign.req.SignApplyReq;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.time.Instant;

@Service
public class HXBKContractInfoService extends AbstractBankContractInfoService {

    private static final Logger logger = LoggerFactory.getLogger(HXBKContractInfoService.class);

    @Autowired
    private CreditRepository creditRepository;

    @Autowired
    private AccountBankCardRepository accountBankCardRepository;

    @Autowired
    private LoanRepository loanRepository;

    @Autowired
    private AccountRepository accountRepository;

    @Override
    public SignApplyReq fetchDynamicSignParam(String businessId, AgreementType agreementType) {
        return null;
    }

    @Override
    public SignApplyReq fetchTemplateSignParam(String businessId, AgreementSignature agreementSignature) {
        return switch (agreementSignature.getLoanStage()) {
            case CREDIT -> getCreditTemplateParam(businessId, agreementSignature.getTemplateNo());
            case LOAN -> getLoanTemplateParam(businessId, agreementSignature.getTemplateNo());
            default -> null;
        };
    }

    private SignApplyReq getLoanTemplateParam(String businessId, String templateNo) {
        return getLoanParam(businessId, templateNo);
    }
    private SignApplyReq getLoanParam(String businessId, String templateNo) {
        Loan loan = loanRepository.findById(businessId).orElseThrow();
        Credit credit = creditRepository.findById(loan.getCreditId()).orElseThrow();
        Account account = accountRepository.findById(credit.getAccountId()).orElseThrow();
//        List<AccountBankCard> byCardNoS = accountBankCardRepository.findByCardNo(loan.getLoanCardId());
//        AccountBankCard accountBankCard = byCardNoS.get(0);

        AccountBankCard accountBankCard =accountBankCardRepository.findById(loan.getLoanCardId()).orElseThrow();
        SignApplyReq signApplyReq = new SignApplyReq();
        signApplyReq.setContractCode(templateNo);
        signApplyReq.setTrafficCode(loan.getFlowChannel().name());
        signApplyReq.setAuthNo(loan.getLoanNo());
        signApplyReq.setCreditId(businessId);
        signApplyReq.setAcctName(loan.getCustName());
        signApplyReq.setAddress(account.getLivingAddress());
        AuthDto authDto = new AuthDto();
        authDto.setCardNo(loan.getCustCertNo());
        authDto.setCardType("CRED_PSN_CH_IDCARD");
        authDto.setName(loan.getCustName());
        long epochMilli = Instant.now().toEpochMilli();
        authDto.setTimeStamp(epochMilli);
        signApplyReq.setAuthDto(authDto);
        signApplyReq.setBankName(accountBankCard.getBankName());
        signApplyReq.setBankCardNo(accountBankCard.getCardNo());
        signApplyReq.setPhone(loan.getCustMobile());
        return signApplyReq;
    }

    private SignApplyReq getCreditTemplateParam(String businessId, String templateNo) {
        Credit credit = creditRepository.findById(businessId).orElseThrow();
        AccountBankCard accountBankCard = accountBankCardRepository.findById(credit.getCardId()).orElseThrow();
        Account account = accountRepository.findById(credit.getAccountId()).orElseThrow();
        SignApplyReq signApplyReq = new SignApplyReq();
        signApplyReq.setContractCode(templateNo);
        signApplyReq.setTrafficCode(credit.getFlowChannel().name());
        signApplyReq.setAddress(account.getLivingAddress());
        signApplyReq.setAuthNo(credit.getCreditNo());
        signApplyReq.setCreditId(businessId);
        signApplyReq.setAcctName(credit.getCustName());
        AuthDto authDto = new AuthDto();
        authDto.setCardNo(credit.getCustCertNo());
        authDto.setCardType("CRED_PSN_CH_IDCARD");
        authDto.setName(credit.getCustName());
        long epochMilli = Instant.now().toEpochMilli();
        authDto.setTimeStamp(epochMilli);
        signApplyReq.setAuthDto(authDto);
        signApplyReq.setBankName(accountBankCard.getCardName());
        signApplyReq.setBankCardNo(accountBankCard.getCardNo());
        signApplyReq.setPhone(credit.getCustMobile());
        return signApplyReq;
    }

    @Override
    public boolean isSupport(BankChannel channel) {
        return BankChannel.HXBK == channel;
    }
}
