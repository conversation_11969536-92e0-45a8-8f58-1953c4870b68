package com.jinghang.capital.core.banks.hxbk.service;

import com.jinghang.capital.api.dto.FundingModel;
import com.jinghang.capital.api.dto.LoanPurpose;
import com.jinghang.capital.core.banks.hxbk.config.HXBKConfig;
import com.jinghang.capital.core.banks.hxbk.enums.HXBKLoanPurpose;
import com.jinghang.capital.core.banks.hxbk.enums.HXBKResponseCode;
import com.jinghang.capital.core.convert.EnumConvert;
import com.jinghang.capital.core.dto.ContractBizDto;
import com.jinghang.capital.core.entity.*;
import com.jinghang.capital.core.enums.*;
import com.jinghang.capital.core.exception.BizErrorCode;
import com.jinghang.capital.core.exception.BizException;
import com.jinghang.capital.core.repository.AgreementSignatureRepository;
import com.jinghang.capital.core.service.WarningService;
import com.jinghang.capital.core.util.IdGenUtil;
import com.jinghang.capital.core.vo.credit.*;
import com.jinghang.capital.core.banks.AbstractBankCreditService;
import com.jinghang.capital.core.banks.hxbk.dto.credit.*;
import com.jinghang.capital.core.banks.hxbk.callback.dto.CreditResultCallbackRequest;
import com.jinghang.capital.core.banks.hxbk.remote.HXBKRequestService;
import com.jinghang.capital.core.banks.hxbk.util.HXBKDataBuilder;
import com.jinghang.capital.core.banks.hxbk.enums.HXBKIncome;
import com.jinghang.capital.core.repository.AccountContactInfoRepository;
import com.jinghang.common.util.CollectionUtil;
import com.jinghang.common.util.JsonUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.*;

/**
 * HXBK授信服务
 *
 * @Author: Lior
 * @CreateTime: 2025/7/8 11:00
 */
@Service
@Qualifier("HXBKCreditService")
public class HXBKCreditService extends AbstractBankCreditService {

    private static final Logger logger = LoggerFactory.getLogger(HXBKCreditService.class);

    private HXBKConfig hxbkConfig;
    private HXBKLocalValid hxbkLocalValid;
    private HXBKRequestService hxbkRequestService;
    private AccountContactInfoRepository accountContactInfoRepository;
    private AgreementSignatureRepository agreementSignatureRepository;
    private HXBKImageFileService hxbkImageFileService;
    private WarningService warningService;

    /**
     * 开始签章
     * 此方法用于处理签章，不是开始授信
     *
     * @param credit 授信
     * @return
     */
    @Override
    protected CreditResultVo bankCreditApply(Credit credit) {
        CreditResultVo creditResultVo = new CreditResultVo();

        // 2025年7月18日 湖消全部自己签章，这里不需要我们签
//        // FlowChannel是绿信、拍拍的话，不走签章
//        List<FlowChannel> notNeedSignature = List.of(FlowChannel.LVXIN, FlowChannel.PPCJDL);
//        if (notNeedSignature.contains(credit.getFlowChannel())) {
//            creditResultVo.setStatus(ProcessStatus.PROCESSING);
//            return creditResultVo;
//        }
//
//        // 授信阶段签章文件数
//        List<AgreementType> agrTypes = AgreementType.getAgreement(credit.getChannel(), LoanStage.CREDIT, SignatureType.TEMPLATE);
//
//        Account account = getCommonService().findAccountById(credit.getAccountId());
//        for (AgreementType fileType : agrTypes) {
//            AgreementSignature agreementSignature = generateAgrSignature(credit.getId(), fileType, SignatureType.TEMPLATE);
//            agreementSignature.setBankMobilePhone(account.getMobile());
//            agreementSignature.setIdentNo(account.getCertNo());
//            agreementSignature.setPersonName(account.getName());
//            // 文件模板编号
//            agreementSignature.setTemplateNo(fileType.getTemplateNo());
//            agreementSignature.setAddress(account.getLivingAddress());
//            AgreementSignature saved = agreementSignatureRepository.save(agreementSignature);
//            // 签章申请监听
//            getMqService().signatureApply(saved.getId());
//        }

        creditResultVo.setStatus(ProcessStatus.PROCESSING);
        return creditResultVo;
    }

    @Override
    protected Credit bankCreditValidate(CreditApplyVo<ExtInfoVo> applyVo) { // 此bankChannelType 是富民的
        // 蚂蚁授信前, 本地前筛校验
        if (!hxbkLocalValid.validCreditAmount(applyVo.getCreditAmt())) {
            throw new BizException(BizErrorCode.CREDIT_AMOUNT_LIMIT);
        }

        if (!hxbkLocalValid.validPeriods(applyVo.getPeriods())) {
            throw new BizException(BizErrorCode.CREDIT_PERIODS_LIMIT);
        }

        String certNo = applyVo.getIdCardInfo().getCertNo();
        if (!hxbkLocalValid.isValidAge(certNo)) {
            throw new BizException(BizErrorCode.CREDIT_AGE_LIMIT);
        }

        // 身份证有效期校验
        if (!hxbkLocalValid.isValidCertExpiry(applyVo.getIdCardInfo().getCertValidEnd())) {
            throw new BizException(BizErrorCode.CERT_EXPIRE_DAYS_ERROR);
        }

        // 可授信
        Credit credit = getCommonService().creditApply(applyVo);
        credit.setCreditType(CreditType.NORMAL_CREDIT);
        return getCommonService().getCreditRepository().save(credit);
    }

    /**
     * 生成签章记录
     *
     * @param creditId
     * @param fileType
     * @param signatureType
     * @return
     */
    private AgreementSignature generateAgrSignature(String creditId, AgreementType fileType, SignatureType signatureType) {
        AgreementSignature signature = new AgreementSignature();
        signature.setBusinessId(creditId);
        signature.setChannel(BankChannel.HXBK);
        signature.setFileType(fileType.getFileType());
        signature.setSignState(ProcessStatus.INIT);
        signature.setLoanStage(LoanStage.CREDIT);
        signature.setSignatureType(signatureType);
        signature.setDynamicOssBucket("");
        signature.setDynamicOssKey("");
        return signature;
    }

    /**
     * 上传资方授信阶段合同， 监听入口
     * <p>
     * 需要调用监测文件是否签署成功
     * 【【成功后调用资方授信接口】】
     *
     * @param creditId 授信id
     */
    @Override
    public void contractUpload(String creditId) {
        Credit credit = getCommonService().findCreditById(creditId);
        Account account = getCommonService().findAccountById(credit.getAccountId());

        // 2025年7月18日 湖消全部自己签章，这里不需要我们签
        // 绿信和拍拍不需要签章
//        List<FlowChannel> notNeedSignature = List.of(FlowChannel.LVXIN, FlowChannel.PPCJDL);
//        if (!notNeedSignature.contains(credit.getFlowChannel())) {
//            // 授信阶段签章文件数
//            List<AgreementType> agrTypes = AgreementType.getAgreement(credit.getChannel(), LoanStage.CREDIT, SignatureType.TEMPLATE);
//            List<FileType> fileTypes = agrTypes.stream().map(AgreementType::getFileType).toList();
//            // 判断授信前签章是否签署成功
//            List<LoanFile> loanFiles = fetchAllNeedFile(credit, fileTypes);
//            logger.info("授信文件: {}", JSONObject.toJSONString(loanFiles));
//            logger.info("授信文件数量: {}", loanFiles.size());
//            if (loanFiles.size() != 2) {
//                logger.info("HXBK credit sign file not ok, creditId: {}", credit);
//                sendContractUploadDelayMessage(creditId);
//                return;
//            }
//        }

        // 先获取授信流水记录，没有则创建初始的授信流水记录
        HXBKCreditFlow creditFlow = getCommonService().getHXBKCreditFlowRepository()
                .findByCreditId(credit.getId()).orElse(new HXBKCreditFlow());
        creditFlow.setCreditId(credit.getId());
        HXBKCreditFlow savedCreditFlow = getCommonService().getHXBKCreditFlowRepository().save(creditFlow);

        logger.info("HXBK银行授信申请开始, creditId: {}", credit.getId());
        try {
            // 获取影像件，并且上传到蚂蚁sftp中，然后组装影像件参数，放入授信请求中
            List<LoanFile> imageFileList = getCommonService().getLoanFileRepository().findByCreditId(creditId);
            if (CollectionUtil.isEmpty(imageFileList)) {
                logger.info("HXBK授信申请, 未找到授信影像件, creditId: {}", credit.getId());
                return;
            } else if (imageFileList.size() != 3) {
                logger.info("HXBK授信申请, 未找到需要的影像件类型(ID_HEAD/ID_NATION/ID_FACE), creditId: {}", credit.getId());
                return;
            } else if (imageFileList.stream().anyMatch(lf -> !FileType.ID_HEAD.equals(lf.getFileType()) && !FileType.ID_NATION.equals(lf.getFileType()) && !FileType.ID_FACE.equals(lf.getFileType()))) {
                logger.info("HXBK授信申请, 未找到需要的影像件类型(ID_HEAD/ID_NATION/ID_FACE), creditId: {}", credit.getId());
                return;
            }

            HXBKMaterial[] materials;

            // 异常判断，如发现异常，直接告警并return
            try {
//                if (hxbkImageFileService.checkImageFilesExist(credit.getId())) {
//                    // 检查蚂蚁SFTP是否存在该用户的影像件 如果有，则跳过下方的上传
//                    logger.info("HXBK授信申请, 蚂蚁SFTP已存在该用户的影像件, creditId: {}", credit.getId());
//                } else {
//                    // 上传影像件到蚂蚁SFTP，并获取资料列表
//                    materials = hxbkImageFileService.uploadImageFilesToAntSftp(credit, account, imageFileList);
//                    // 校验是否上传成功
//                    if (CollectionUtil.isEmpty(materials) || materials.size() != 3) {
//                        logger.info("HXBK授信申请, 上传影像件到蚂蚁SFTP失败, creditId: {}", credit.getId());
//                        return;
//                    }
//                }
                // 组装影像件资料列表
                materials = hxbkImageFileService.buildImageMaterialList(imageFileList, "credit");
                // 校验是否上传成功
                if (CollectionUtil.isEmpty(Arrays.asList(materials)) || materials.length != 3) {
                    logger.info("HXBK授信申请, 组装影像件资料列表失败, creditId: {}", credit.getId());
                    return;
                }
            } catch (Exception e) {
                warningService.warn("HXBK授信申请, 组装影像件资料列表失败, creditId: " + credit.getId() + ", errorMsg: " + e.getMessage());
                logger.error("HXBK授信申请, 组装影像件资料列表失败, creditId: {}", credit.getId(), e);
                return;
            }

            // 构建授信申请请求
            HXBKCreditApplyRequest request = buildCreditApplyRequest(credit, account, materials);

            // 调用HXBK授信接口
            logger.info("HXBK授信申请=============开始");
            HXBKCreditApplyResponse response = hxbkRequestService.creditApply(request);
            logger.info("HXBK授信申请=============结束");

            // 处理授信申请响应：空值处理
            if (Objects.isNull(response)) {
                logger.error("HXBK授信申请, 授信申请接口调用失败, response is null, creditId: {}", credit.getId());
                // 丢延迟队列，异步查询授信结果
                getMqService().submitCreditResultQueryDelay(creditId);
                return;
            }

            // 处理授信申请响应：异常处理
            if (HXBKResponseCode.isNotOk(response.getResultCode())) {
                logger.error("HXBK授信申请, 授信申请接口调用失败，授信申请置为失败，不再重试！creditId: {}, response: {}", credit.getId(), JsonUtil.toJsonString(response));
                // 丢延迟队列，异步查询授信结果
//                getMqService().submitCreditResultQueryDelay(creditId);

                // 2025年7月30日 蚂蚁反馈除了ok都是失败，而且无法发起重试，所以这里直接改成失败
                creditFail(credit, response.getResultMsg());
                return;
            }

            // 处理授信申请响应：成功处理
            // 更新授信流水记录
            savedCreditFlow.setCustomNo(response.getCustomNo());
            getCommonService().getHXBKCreditFlowRepository().save(savedCreditFlow);

            // 保存或更新hxbk_account_attr账户字段
            saveOrUpdateAccountAttr(request, credit);

            // 丢延迟队列，异步查询授信结果
            getMqService().submitCreditResultQueryDelay(creditId);
        } catch (Exception e) {
            // 遇到异常,不在这里处理,直接抛出异常
            logger.error("HXBK授信申请异常, creditId: {}", credit.getId(), e);
            throw e;
        }
    }

    /**
     * 发送授信结果查询延迟消息
     *
     * @param businessId 授信ID
     */
    private void sendContractUploadDelayMessage(String businessId) {
        // 监测授信阶段文件
        ContractBizDto contractBizDto = new ContractBizDto();
        contractBizDto.setStage(LoanStage.CREDIT);
        contractBizDto.setBusinessId(businessId);
        getMqService().submitContractUploadDelay(JsonUtil.toJsonString(contractBizDto));
    }

    // 查询签署完成协议文件
    private List<LoanFile> fetchAllNeedFile(Credit credit, List<FileType> fileTypes) {
        return getCommonService().getLoanFileRepository().findByCreditIdAndFileTypeList(credit.getId(), LoanStage.CREDIT.name(), fileTypes);
    }

    /**
     * 授信失败处理
     *
     * @param credit     授信记录
     * @param failReason 失败原因
     */
    private void creditFail(Credit credit, String failReason) {
        credit.setRemark(failReason);
        credit.setCreditStatus(CreditStatus.FAIL);
        getCommonService().saveCredit(credit);
    }

    /**
     * 授信结果查询
     *
     * @param credit  授信
     * @param queryVo
     * @return
     */
    @Override
    protected CreditResultVo bankCreditQuery(Credit credit, CreditQueryVo queryVo) {
        // 构建查询请求
        HXBKCreditQueryRequest request = new HXBKCreditQueryRequest();
        // 每次重新生成
        request.setOrderNo(IdGenUtil.genReqNo());
        // 授信申请的订单号
        request.setOriginalOrderNo(credit.getId());

        // 调用查询接口
        logger.info("HXBK授信查询=============开始");
        HXBKCreditQueryResponse response = hxbkRequestService.creditQuery(request);
        logger.info("HXBK授信查询=============结束");

        CreditResultVo creditResultVo = new CreditResultVo();
        creditResultVo.setStatus(ProcessStatus.PROCESSING);

        if (HXBKResponseCode.isNotOk(response.getResultCode())) {
            logger.error("HXBK授信查询失败，响应码异常，creditId: {}, response: {}", credit.getId(), JsonUtil.toJsonString(response));
            warningService.warn("HXBK授信查询失败，响应码异常，creditId: " + credit.getId() + ", response: " + JsonUtil.toJsonString(response));
            throw new BizException(response.getResultCode(), response.getResultMsg());
        }

        // 处理授信查询结果
        creditResultVo = processCreditQueryResult(credit, response);
        return creditResultVo;
    }

    @Override
    protected void bankCreditApplyResult(Credit credit, CreditResultVo resultVo) {
        credit.setCreditStatus(EnumConvert.INSTANCE.toCreditStatus(resultVo.getStatus()));
        getCommonService().saveCredit(credit);

        if (resultVo.getStatus() == ProcessStatus.PROCESSING) {
            sendContractUploadMessage(credit.getId());
        }
    }

    /**
     * 发送授信结果查询延迟消息
     *
     * @param businessId
     */
    private void sendContractUploadMessage(String businessId) {
        // 监测授信阶段文件
        ContractBizDto contractBizDto = new ContractBizDto();
        contractBizDto.setStage(LoanStage.CREDIT);
        contractBizDto.setBusinessId(businessId);
        getMqService().submitContractUpload(JsonUtil.toJsonString(contractBizDto));
    }

    /**
     * 构建HXBK授信申请请求
     *
     * @param credit    授信记录
     * @param account   账户信息
     * @param materials 资料列表
     * @return HXBK授信申请请求
     */
    private HXBKCreditApplyRequest buildCreditApplyRequest(Credit credit, Account account, HXBKMaterial[] materials) {
        List<AccountContactInfo> contactInfos = accountContactInfoRepository.findByAccountId(account.getId());

        HXBKCreditApplyRequest request = new HXBKCreditApplyRequest();

        // 超捷生成
        request.setOrderNo(credit.getId());
        // 默认APP
        request.setChannelType("1");
        // 默认新增客户
        request.setCustomType("1");
        // 资金方代码 固定值 D20250701000000001
        request.setFundCode(hxbkConfig.getFundCode());
        // 超捷生成 唯一值 这个值需要和身份证号一起存起来 落库，md5(身份证号)
        request.setOpenId(IdGenUtil.md5Encrypt(account.getCertNo()));
        // 使用工具类构建各种信息
        request.setPersonalInfo(HXBKDataBuilder.buildPersonalInfo(account));
        request.setBorrowerEmpInfo(HXBKDataBuilder.buildJobInfo(account));
        request.setBorrowerLiveInfo(HXBKDataBuilder.buildLiveInfo(account));
        // 风险数据JSON字符串
        credit.setBankRate(BigDecimal.valueOf(hxbkConfig.getCreditRate()));
        request.setRiskData(HXBKDataBuilder.buildRiskDataJson(credit, contactInfos));
        // 借款原因 非必填
        request.setLoanReason(convertContactType(credit.getLoanPurpose()));
        // 申请金额
        if (credit.getLoanAmt() != null) {
            request.setApplicationsAmount(credit.getLoanAmt().toString());
        }
        // 申请期限
        request.setApplicationDeadline(credit.getPeriods().toString());
        // 发证机关
        request.setAuthorityName(account.getCertSignOrg());
        // 月收入
        String monthSalary = HXBKIncome.convertIncomeToMonthSalary(account.getIncome());
        request.setMonthSalary(monthSalary);

        // 设置资料列表
        request.setMaterials(materials);

        return request;
    }

    /**
     * 转换借款原因类型
     *
     * @param loanPurpose 关系枚举
     * @return HXBK借款原因编码
     */
    public static String convertContactType(LoanPurpose loanPurpose) {
        return HXBKLoanPurpose.getCodeEnumByLoanPurpose(loanPurpose).getCode();
    }

    /**
     * 处理授信查询结果
     *
     * @param credit   授信记录
     * @param response HXBK查询响应
     * @return 授信结果VO
     */
    private CreditResultVo processCreditQueryResult(Credit credit, HXBKCreditQueryResponse response) {
        // 使用统一的授信结果处理方法
        return processCreditResultCommon(credit, response, null, "query");
    }

    /**
     * 统一处理授信结果的通用方法
     * 用于处理主动查询和回调通知的授信结果
     *
     * @param credit          授信记录
     * @param queryResponse   主动查询响应对象（当source为QUERY时使用）
     * @param callbackRequest 回调请求对象（当source为CALLBACK时使用）
     * @param source          授信结果来源
     * @return 授信结果VO
     */
    public CreditResultVo processCreditResultCommon(Credit credit, HXBKCreditQueryResponse queryResponse,
                                                    CreditResultCallbackRequest callbackRequest,
                                                    String source) {
        logger.info("开始统一处理授信结果，creditId: {}, source: {}", credit.getId(), source);

        // 根据来源提取通用字段
        String status;
        BigDecimal creditAmt;
        String failMsg;
        String creditSeq;
        Date payDate = null;
        Date expireDate = null;

        if ("query".equals(source)) {
            // 从查询响应中提取数据
            status = queryResponse.getStatus();
            creditAmt = queryResponse.getCreditAmt();
            failMsg = queryResponse.getMsg();
            creditSeq = queryResponse.getApplyNo();
            if (queryResponse.getCreditInfo() != null) {
                payDate = queryResponse.getCreditInfo().getPayDate();
                expireDate = queryResponse.getCreditInfo().getExpireDate();
            }
        } else {
            // 从回调请求中提取数据
            status = callbackRequest.getStatus();
            creditAmt = callbackRequest.getCreditAmt();
            failMsg = callbackRequest.getMsg();
            creditSeq = callbackRequest.getApplyNo();
            if (callbackRequest.getCreditInfo() != null) {
                payDate = callbackRequest.getCreditInfo().getPayDate();
                expireDate = callbackRequest.getCreditInfo().getExpireDate();
            }
        }

        CreditResultVo resultVo = new CreditResultVo();
        resultVo.setCreditId(credit.getId());
        resultVo.setCreditNo(creditSeq);
        resultVo.setFailMsg(failMsg);
        resultVo.setCreditResultAmt(creditAmt);

        // 根据状态处理结果
        if ("0".equals(status)) {
            // 授信通过
            resultVo.setStatus(ProcessStatus.SUCCESS);

            // 更新授信记录
            credit.setCreditStatus(CreditStatus.SUCCESS);

            // 设置通过时间
            if (payDate != null) {
                credit.setPassTime(payDate.toInstant()
                        .atZone(ZoneId.systemDefault()).toLocalDateTime());
            } else {
                credit.setPassTime(LocalDateTime.now());
            }

            // 设置授信有效期
            if (expireDate != null) {
                credit.setCapExpireTime(expireDate.toInstant()
                        .atZone(ZoneId.systemDefault()).toLocalDateTime());
            }
            // callback
            if ("callback".equals(source)) {
                credit.setCreditNo(creditSeq);
                credit.setCreditResultAmt(creditAmt);
                getCommonService().initCreditAvailable(credit);
            }
        } else if ("2".equals(status)) {
            // 处理中
            resultVo.setStatus(ProcessStatus.PROCESSING);
            credit.setCreditStatus(CreditStatus.PROCESSING);

            // callback直接放到延迟队列
            if ("callback".equals(source)) {
                getMqService().submitCreditResultQueryDelay(credit.getId());
            }
        } else {
            // 授信失败
            resultVo.setStatus(ProcessStatus.FAIL);
            credit.setCreditStatus(CreditStatus.FAIL);
            credit.setRemark(failMsg);
        }

        // 保存授信记录 query在AbstractBankCreditService会存储，callback在这里存储
        if ("callback".equals(source)) {
            getCommonService().saveCredit(credit);
        }

        logger.info("授信结果处理完成，creditId: {}, finalStatus: {}", credit.getId(), credit.getCreditStatus());
        return resultVo;
    }

    /**
     * 统一更新授信流水记录
     * 更新：授信申请的时候已经入库了，这里不需要做处理
     *
     * @param creditId   授信ID
     * @param creditSeq  授信序列号
     * @param customNo   客户编号
     * @param creditFlow 现有的授信流水记录
     */
    private void updateCreditFlowCommon(String creditId, String creditSeq, String customNo, HXBKCreditFlow creditFlow) {
        try {
            HXBKCreditFlow flowToUpdate;

            if (creditFlow != null) {
                flowToUpdate = creditFlow;
            } else {
                // 如果没有传入现有记录，尝试查找或创建新记录
                flowToUpdate = getCommonService().getHXBKCreditFlowRepository()
                        .findByCreditId(creditId).orElse(new HXBKCreditFlow());
                flowToUpdate.setCreditId(creditId);
            }

            // 更新流水信息
            flowToUpdate.setFundingModel(FundingModel.ALONE);
            flowToUpdate.setCreditNo(creditSeq);
            flowToUpdate.setCustomNo(customNo);

            getCommonService().getHXBKCreditFlowRepository().save(flowToUpdate);

            logger.info("授信流水记录更新成功，creditId: {}, customNo: {}", creditId, customNo);
        } catch (Exception e) {
            logger.error("更新授信流水记录失败，creditId: {}", creditId, e);
        }
    }

    /**
     * 保存HXBK账户属性
     * 先查询是否存在记录，如果存在则不处理，不存在则新增
     *
     * @param request 授信申请请求
     * @param credit  授信记录
     */
    private void saveOrUpdateAccountAttr(HXBKCreditApplyRequest request, Credit credit) {
        try {
            String certNo = request.getPersonalInfo().getCardNo();
            String name = request.getPersonalInfo().getCustomName();
            String openId = request.getOpenId();

            // 先查询是否存在记录
            Optional<HXBKAccountAttr> existingAccount = getCommonService().getHXBKAccountAttrRepository().findById(openId);

            if (existingAccount.isPresent()) {
                // 如果存在则不处理
                logger.info("HXBK账户属性已存在，跳过保存, id：{},certNo: {}, openId: {}", openId, certNo, openId);
                return;
            }

            // 如果不存在则新增
            HXBKAccountAttr accountAttr = new HXBKAccountAttr();
            // 使用md5(身份证号)作为主键
            accountAttr.setId(openId);
            accountAttr.setName(name);
            accountAttr.setCertNo(certNo);
            accountAttr.setOpenId(openId);

            getCommonService().getHXBKAccountAttrRepository().save(accountAttr);
            logger.info("新增HXBK账户属性成功, certNo: {}, openId: {}", certNo, openId);
        } catch (Exception e) {
            logger.error("保存HXBK账户属性失败, creditId: {}", credit.getId(), e);
        }
    }

    @Override
    protected String getProductType() {
        return null;
    }

    @Override
    public boolean isSupport(BankChannel channel) {
        return BankChannel.HXBK == channel;
    }

    // 依赖注入
    @Autowired
    public void setHxbkRequestService(HXBKRequestService hxbkRequestService) {
        this.hxbkRequestService = hxbkRequestService;
    }

    @Autowired
    public void setAccountContactInfoRepository(AccountContactInfoRepository accountContactInfoRepository) {
        this.accountContactInfoRepository = accountContactInfoRepository;
    }

    @Autowired
    public void setHxbkConfig(HXBKConfig hxbkConfig) {
        this.hxbkConfig = hxbkConfig;
    }

    @Autowired
    public void setAgreementSignatureRepository(AgreementSignatureRepository agreementSignatureRepository) {
        this.agreementSignatureRepository = agreementSignatureRepository;
    }

    @Autowired
    public void setHXBKLocalValid(HXBKLocalValid hxbkLocalValid) {
        this.hxbkLocalValid = hxbkLocalValid;
    }

    @Autowired
    public void setHxbkImageFileService(HXBKImageFileService hxbkImageFileService) {
        this.hxbkImageFileService = hxbkImageFileService;
    }

    @Autowired
    public void setWarningService(WarningService warningService) {
        this.warningService = warningService;
    }
}
