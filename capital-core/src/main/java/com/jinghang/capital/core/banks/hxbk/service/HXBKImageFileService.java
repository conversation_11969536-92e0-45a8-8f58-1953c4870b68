package com.jinghang.capital.core.banks.hxbk.service;

import com.alibaba.fastjson.JSONObject;
import com.jcraft.jsch.JSch;
import com.jcraft.jsch.Session;
import com.jcraft.jsch.SftpException;
import com.jinghang.capital.core.banks.hxbk.config.HXBKConfig;
import com.jinghang.capital.core.banks.hxbk.dto.credit.HXBKMaterial;
import com.jinghang.capital.core.banks.hxbk.dto.repay.HXBKRepayReturnFileDTO;
import com.jinghang.capital.core.entity.*;
import com.jinghang.capital.core.enums.FileType;
import com.jinghang.capital.core.repository.HXBKAntFileLogRepository;
import com.jinghang.capital.core.service.FileService;
import com.jinghang.capital.core.util.IdGenUtil;
import com.jinghang.common.sftp.DestMapping;
import com.jinghang.common.sftp.Sftp;
import com.jinghang.common.util.SftpUtil;
import com.jcraft.jsch.ChannelSftp;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.io.*;
import java.nio.file.Files;
import java.nio.file.Path;
import java.time.Instant;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.Stream;
import java.util.zip.ZipEntry;
import java.util.zip.ZipOutputStream;

/**
 * HXBK影像件上传服务
 *
 * @Author: Lior
 * @CreateTime: 2025/7/11
 */
@Service
public class HXBKImageFileService {

    private static final Logger logger = LoggerFactory.getLogger(HXBKImageFileService.class);

    private HXBKConfig hxbkConfig;
    private FileService fileService;
    private HXBKAntFileLogRepository hxbkAntFileLogRepository;

    @Value("${mayi.sftp.download.repay}")
    private String returnFilePrefix;


    /**
     * 检查影像件是否存在
     * 根据授信申请单号查询蚂蚁的SFTP是否存在身份证、人脸影像件，必须都存在才返回true
     *
     * @param applySerialNo 授信申请单号
     * @return 身份证和人脸影像件都存在返回true，否则返回false
     */
    public boolean checkImageFilesExist(String applySerialNo) {
        try {
            logger.info("开始检查HXBK影像件是否存在, applySerialNo: {}", applySerialNo);

            // 当前日期，用于目录结构
            String currentDate = LocalDate.now().format(DateTimeFormatter.ofPattern("yyyyMMdd"));

            // 创建SFTP连接
            Sftp sftp = SftpUtil.use(hxbkConfig.getSftpUsername(), hxbkConfig.getSftpPassword(),
                    hxbkConfig.getSftpHost(), hxbkConfig.getSftpPort());

            // 检查身份证影像件是否存在
            boolean idCardExists = checkIdCardFilesExist(sftp, applySerialNo, currentDate);

            // 检查人脸影像件是否存在
            boolean faceExists = checkFaceFilesExist(sftp, applySerialNo, currentDate);

            boolean allExists = idCardExists && faceExists;
            logger.info("HXBK影像件检查完成, applySerialNo: {}, 身份证存在: {}, 人脸存在: {}, 全部存在: {}",
                    applySerialNo, idCardExists, faceExists, allExists);

            return allExists;
        } catch (Exception e) {
            logger.error("检查HXBK影像件是否存在失败, applySerialNo: {}", applySerialNo, e);
            return false;
        }
    }

    /**
     * 上传影像件到蚂蚁SFTP
     *
     * @param credit        授信记录
     * @param account       账户信息
     * @param imageFileList 影像件列表
     * @return 资料列表
     */
    public List<HXBKMaterial> uploadImageFilesToAntSftp(Credit credit, Account account, List<LoanFile> imageFileList) {
        try {
            logger.info("开始上传HXBK影像件到蚂蚁SFTP, creditId: {}", credit.getId());

            // 筛选需要的影像件类型：ID_HEAD、ID_NATION、ID_FACE
            List<LoanFile> targetImageFiles = imageFileList.stream()
                    .filter(file -> file.getFileType() == FileType.ID_HEAD ||
                            file.getFileType() == FileType.ID_NATION ||
                            file.getFileType() == FileType.ID_FACE)
                    .toList();

            if (targetImageFiles.isEmpty()) {
                logger.warn("HXBK授信申请, 未找到需要的影像件类型(ID_HEAD/ID_NATION/ID_FACE), creditId: {}", credit.getId());
                return new ArrayList<>();
            }

            // 分类处理：身份证类和人脸类
            List<LoanFile> idCardFiles = targetImageFiles.stream()
                    .filter(file -> file.getFileType() == FileType.ID_HEAD || file.getFileType() == FileType.ID_NATION)
                    .collect(Collectors.toList());

            List<LoanFile> faceFiles = targetImageFiles.stream()
                    .filter(file -> file.getFileType() == FileType.ID_FACE)
                    .collect(Collectors.toList());

            // 授信申请单号
            String applySerialNo = credit.getId();
            // 身份证号
            String idCardNo = account.getCertNo();

            // 用于收集上传后的文件信息
            List<HXBKMaterial> materials = new ArrayList<>();

            // 上传身份证影像件并收集文件信息
            if (!idCardFiles.isEmpty()) {
                List<HXBKMaterial> idCardMaterials = uploadIdCardImagesAndBuildMaterials(idCardFiles, applySerialNo, idCardNo);
                materials.addAll(idCardMaterials);
            }

            // 上传人脸影像件并收集文件信息
            if (!faceFiles.isEmpty()) {
                List<HXBKMaterial> faceMaterials = uploadFaceImagesAndBuildMaterials(faceFiles, applySerialNo);
                materials.addAll(faceMaterials);
            }

            logger.info("HXBK影像件上传完成, creditId: {}", credit.getId());
            return materials;

        } catch (Exception e) {
            logger.error("HXBK影像件上传失败, creditId: {}", credit.getId(), e);
            throw new RuntimeException("影像件上传失败", e);
        }
    }

    /**
     * 上传身份证影像件并构建资料信息
     *
     * @param idCardFiles   身份证文件列表
     * @param applySerialNo 申请流水号
     * @param idCardNo      身份证号
     * @return 身份证资料列表
     */
    private List<HXBKMaterial> uploadIdCardImagesAndBuildMaterials(List<LoanFile> idCardFiles, String applySerialNo, String idCardNo) {
        try {
            logger.info("开始上传身份证影像件, applySerialNo: {}", applySerialNo);

            // 当前日期，用于目录结构
            String currentDate = LocalDate.now().format(DateTimeFormatter.ofPattern("yyyyMMdd"));

            // 生成批次号
            String batchNo = generateBatchNo();

            // 创建临时目录
            Path tempDir = Files.createTempDirectory("hxbk_idcard_");

            try {
                // 准备文件数据
                Map<String, byte[]> fileDataMap = new HashMap<>();
                List<String> indexLines = new ArrayList<>();

                for (LoanFile idCardFile : idCardFiles) {
                    // 从OSS下载文件
                    byte[] fileData = downloadFileFromOss(idCardFile);

                    // 生成文件名
                    String suffix = (idCardFile.getFileType() == FileType.ID_HEAD) ? "_x.jpg" : "_y.jpg";
                    String fileName = applySerialNo + "_" + idCardNo + suffix;

                    // 存储文件数据
                    fileDataMap.put(fileName, fileData);

                    // 生成索引行
                    String fileTypeCode = (idCardFile.getFileType() == FileType.ID_HEAD) ? "IDCARDX" : "IDCARDY";

                    indexLines.add(String.format("TIANSHU,PLATFORM1,%s,%s,%s", applySerialNo, fileTypeCode, fileName));
                }

                // 创建ZIP文件
                String zipFileName = "IDCARD-" + batchNo + ".zip";
                Path zipFilePath = tempDir.resolve(zipFileName);
                createZipFile(zipFilePath, fileDataMap);

                // 创建索引文件
                String indexFileName = zipFileName + ".txt";
                Path indexFilePath = tempDir.resolve(indexFileName);
                Files.writeString(indexFilePath, String.join("\n", indexLines));

                // 上传到SFTP
                String remoteDir = hxbkConfig.getIdCardDir() + currentDate;
                uploadToSftp(zipFilePath, remoteDir, zipFileName, applySerialNo);
                uploadToSftp(indexFilePath, remoteDir, indexFileName, applySerialNo);

                logger.info("身份证影像件上传成功, batchNo: {}, fileCount: {}", batchNo, idCardFiles.size());

                // 构建身份证资料信息
                List<HXBKMaterial> materials = new ArrayList<>();

                for (LoanFile idCardFile : idCardFiles) {
                    // 获取OSS URL并拆分成文件名和路径
                    String[] urlParts = getOssUrlAndSplit(idCardFile);
                    String fileName = urlParts[0];
                    String filePath = urlParts[1];

                    HXBKMaterial material = new HXBKMaterial();
                    // 图片类型
                    material.setMType("2");
                    // 身份证图片
                    material.setBigCode("20");
                    // 文件名 - 只要文件名部分
                    material.setMeterialName(fileName);
                    // 完整路径 - 只要路径部分（结尾不带斜杠）
                    material.setFilePath(filePath);

                    if (idCardFile.getFileType() == FileType.ID_HEAD) {
                        // 身份证人脸面
                        material.setSmallCode("201");
                    } else if (idCardFile.getFileType() == FileType.ID_NATION) {
                        // 身份证国徽面
                        material.setSmallCode("202");
                    }

                    materials.add(material);
                }

                return materials;

            } finally {
                // 清理临时目录
                deleteDirectory(tempDir);
            }

        } catch (Exception e) {
            logger.error("身份证影像件上传失败, applySerialNo: {}", applySerialNo, e);
            throw new RuntimeException("身份证影像件上传失败", e);
        }
    }

    /**
     * 上传人脸影像件并构建资料信息
     *
     * @param faceFiles     人脸文件列表
     * @param applySerialNo 申请流水号
     * @return 人脸资料列表
     */
    private List<HXBKMaterial> uploadFaceImagesAndBuildMaterials(List<LoanFile> faceFiles, String applySerialNo) {
        try {
            logger.info("开始上传人脸影像件, applySerialNo: {}", applySerialNo);

            // 当前日期，用于目录结构
            String currentDate = LocalDate.now().format(DateTimeFormatter.ofPattern("yyyyMMdd"));

            // 生成批次号
            String batchNo = generateBatchNo();

            // 创建临时目录
            Path tempDir = Files.createTempDirectory("hxbk_photo_");

            try {
                // 准备文件数据
                Map<String, byte[]> fileDataMap = new HashMap<>();
                List<String> indexLines = new ArrayList<>();

                for (LoanFile faceFile : faceFiles) {
                    // 从OSS下载文件
                    byte[] fileData = downloadFileFromOss(faceFile);

                    // 生成文件名
                    String fileName = applySerialNo + ".jpg";

                    // 存储文件数据
                    fileDataMap.put(fileName, fileData);

                    // 生成索引行
                    indexLines.add(String.format("TIANSHU,PLATFORM1,%s,PHOTO,%s", applySerialNo, fileName));
                }

                // 创建ZIP文件
                String zipFileName = "PHOTO-" + batchNo + ".zip";
                Path zipFilePath = tempDir.resolve(zipFileName);
                createZipFile(zipFilePath, fileDataMap);

                // 创建索引文件
                String indexFileName = zipFileName + ".txt";
                Path indexFilePath = tempDir.resolve(indexFileName);
                Files.writeString(indexFilePath, String.join("\n", indexLines));

                // 上传到SFTP
                String remoteDir = hxbkConfig.getPhotoDir() + currentDate;
                uploadToSftp(zipFilePath, remoteDir, zipFileName, applySerialNo);
                uploadToSftp(indexFilePath, remoteDir, indexFileName, applySerialNo);

                logger.info("人脸影像件上传成功, batchNo: {}, fileCount: {}", batchNo, faceFiles.size());

                // 构建人脸资料信息
                List<HXBKMaterial> materials = new ArrayList<>();

                for (LoanFile faceFile : faceFiles) {
                    // 获取OSS URL并拆分成文件名和路径
                    String[] urlParts = getOssUrlAndSplit(faceFile);
                    String fileName = urlParts[0];
                    String filePath = urlParts[1];

                    HXBKMaterial material = new HXBKMaterial();
                    // 图片类型
                    material.setMType("2");
                    // 人脸图片
                    material.setBigCode("26");
                    // 活体人脸图片
                    material.setSmallCode("212");
                    // 文件名 - 只要文件名部分
                    material.setMeterialName(fileName);
                    // 完整路径 - 只要路径部分（结尾不带斜杠）
                    material.setFilePath(filePath);
                    materials.add(material);
                }

                return materials;

            } finally {
                // 清理临时目录
                deleteDirectory(tempDir);
            }

        } catch (Exception e) {
            logger.error("人脸影像件上传失败, applySerialNo: {}", applySerialNo, e);
            throw new RuntimeException("人脸影像件上传失败", e);
        }
    }

    /**
     * 从OSS下载文件
     *
     * @param loanFile 文件记录
     * @return 文件字节数组
     */
    private byte[] downloadFileFromOss(LoanFile loanFile) {
        try (InputStream inputStream = fileService.getOssFile(loanFile.getOssBucket(), loanFile.getOssKey());
             ByteArrayOutputStream outputStream = new ByteArrayOutputStream()) {

            byte[] buffer = new byte[1024];
            int bytesRead;
            while ((bytesRead = inputStream.read(buffer)) != -1) {
                outputStream.write(buffer, 0, bytesRead);
            }

            return outputStream.toByteArray();
        } catch (Exception e) {
            logger.error("从OSS下载文件失败, bucket: {}, key: {}", loanFile.getOssBucket(), loanFile.getOssKey(), e);
            throw new RuntimeException("文件下载失败", e);
        }
    }


    /**
     * 生成批次号
     * CJyyMMddHHmmssSSS+三位随机数
     *
     * @return 批次号
     */
    private String generateBatchNo() {
        return IdGenUtil.genReqNo("CJ", 20);
    }

    /**
     * 创建ZIP文件
     *
     * @param zipFilePath ZIP文件路径
     * @param fileDataMap 文件数据映射
     */
    private void createZipFile(Path zipFilePath, Map<String, byte[]> fileDataMap) throws IOException {
        try (ZipOutputStream zipOut = new ZipOutputStream(Files.newOutputStream(zipFilePath))) {
            for (Map.Entry<String, byte[]> entry : fileDataMap.entrySet()) {
                ZipEntry zipEntry = new ZipEntry(entry.getKey());
                zipOut.putNextEntry(zipEntry);
                zipOut.write(entry.getValue());
                zipOut.closeEntry();
            }
        }
    }

    /**
     * 上传文件到SFTP
     *
     * @param filePath  本地文件路径
     * @param remoteDir 远程目录
     * @param fileName  文件名
     * @param creditId  授信ID
     */
    private void uploadToSftp(Path filePath, String remoteDir, String fileName, String creditId) {
        try {
            // 构建完整的远程文件路径
            String sftpFilePath = remoteDir + "/" + fileName;
            String localFilePath = filePath.toAbsolutePath().toString();

            // 创建DestMapping
            DestMapping uploadMapping = new DestMapping(sftpFilePath, localFilePath);

            // 创建SFTP连接
            Sftp sftp = SftpUtil.use(hxbkConfig.getSftpUsername(), hxbkConfig.getSftpPassword(),
                    hxbkConfig.getSftpHost(), hxbkConfig.getSftpPort());

            // 上传文件
            sftp.upload(uploadMapping);

            logger.info("文件上传SFTP成功: {}", sftpFilePath);

            // 记录文件上传日志到数据库
            saveFileUploadLog(creditId, sftpFilePath, fileName);

        } catch (Exception e) {
            logger.error("文件上传SFTP失败: {}/{}", remoteDir, fileName, e);
            throw new RuntimeException("SFTP上传失败", e);
        }
    }

    /**
     * 保存文件上传日志
     *
     * @param creditId 授信ID
     * @param filePath 文件路径
     * @param fileName 文件名
     */
    private void saveFileUploadLog(String creditId, String filePath, String fileName) {
        try {
            HXBKAntFileLog fileLog = new HXBKAntFileLog();
            fileLog.setCreditId(creditId);
            fileLog.setFilePath(filePath);
            fileLog.setFileName(fileName);
            fileLog.setRemark("HXBK影像件上传到蚂蚁SFTP");

            hxbkAntFileLogRepository.save(fileLog);
            logger.info("保存文件上传日志成功, creditId: {}, fileName: {}", creditId, fileName);
        } catch (Exception e) {
            logger.error("保存文件上传日志失败, creditId: {}, fileName: {}", creditId, fileName, e);
        }
    }

    /**
     * 检查身份证影像件是否存在
     *
     * @param sftp          SFTP连接
     * @param applySerialNo 申请流水号
     * @param currentDate   当前日期
     * @return 身份证影像件存在返回true，否则返回false
     */
    private boolean checkIdCardFilesExist(Sftp sftp, String applySerialNo, String currentDate) {
        try {
            String remoteDir = hxbkConfig.getIdCardDir() + currentDate;
            logger.info("检查身份证影像件目录: {}", remoteDir);

            // 使用数组来存储结果，因为lambda表达式中需要final变量
            final boolean[] result = {false};

            sftp.custom(channelSftp -> {
                try {
                    // 查找匹配的身份证ZIP文件
                    Vector<ChannelSftp.LsEntry> files = channelSftp.ls(remoteDir + "/IDCARD-*.zip");
                    logger.info("待匹配文件：{}", JSONObject.toJSONString(files));

                    if (files.isEmpty()) {
                        logger.info("未找到身份证影像件, applySerialNo: {}, 目录: {}", applySerialNo, remoteDir);
                        result[0] = false;
                        return;
                    }

                    // 检查是否有包含申请单号的文件
                    boolean found = files.stream()
                            .anyMatch(entry -> {
                                String fileName = entry.getFilename();
                                logger.debug("检查身份证文件: {}", fileName);
                                // 检查文件名是否包含申请单号相关信息
                                return fileName.contains(applySerialNo) && fileName.startsWith("IDCARD-") && fileName.endsWith(".zip");
                            });

                    logger.info("身份证影像件检查结果, applySerialNo: {}, 存在: {}", applySerialNo, found);
                    result[0] = found;
                } catch (Exception e) {
                    logger.warn("检查身份证影像件失败, applySerialNo: {}, 目录: {}", applySerialNo, remoteDir, e);
                    result[0] = false;
                }
            });

            return result[0];
        } catch (Exception e) {
            logger.error("检查身份证影像件异常, applySerialNo: {}", applySerialNo, e);
            return false;
        }
    }

    /**
     * 检查人脸影像件是否存在
     *
     * @param sftp          SFTP连接
     * @param applySerialNo 申请流水号
     * @param currentDate   当前日期
     * @return 人脸影像件存在返回true，否则返回false
     */
    private boolean checkFaceFilesExist(Sftp sftp, String applySerialNo, String currentDate) {
        try {
            String remoteDir = hxbkConfig.getPhotoDir() + "/" + currentDate;
            logger.info("检查人脸影像件目录: {}", remoteDir);

            // 使用数组来存储结果，因为lambda表达式中需要final变量
            final boolean[] result = {false};

            sftp.custom(channelSftp -> {
                try {
                    // 查找匹配的人脸ZIP文件
                    Vector<ChannelSftp.LsEntry> files = channelSftp.ls(remoteDir + "/PHOTO-*.zip");

                    if (files.isEmpty()) {
                        logger.info("未找到人脸影像件, applySerialNo: {}, 目录: {}", applySerialNo, remoteDir);
                        result[0] = false;
                        return;
                    }

                    // 检查是否有包含申请单号的文件
                    boolean found = files.stream()
                            .anyMatch(entry -> {
                                String fileName = entry.getFilename();
                                logger.debug("检查人脸文件: {}", fileName);
                                // 检查文件名是否包含申请单号相关信息 需要加上applySerialNo
                                return fileName.contains(applySerialNo) && fileName.startsWith("PHOTO-") && fileName.endsWith(".zip");
                            });

                    logger.info("人脸影像件检查结果, applySerialNo: {}, 存在: {}", applySerialNo, found);
                    result[0] = found;
                } catch (Exception e) {
                    logger.warn("检查人脸影像件失败, applySerialNo: {}, 目录: {}", applySerialNo, remoteDir, e);
                    result[0] = false;
                }
            });

            return result[0];
        } catch (Exception e) {
            logger.error("检查人脸影像件异常, applySerialNo: {}", applySerialNo, e);
            return false;
        }
    }

    /**
     * 组装影像件资料列表（与SFTP无关）
     * 根据影像件列表生成HXBK Material列表，使用原始文件的OSS URL
     *
     * @param imageFileList 影像件列表
     * @param type          类型：credit-授信；usecredit-用信
     * @return 资料列表
     */
    public HXBKMaterial[] buildImageMaterialList(List<LoanFile> imageFileList, String type) {
        try {
            logger.info("开始组装HXBK影像件资料列表, fileCount: {}", imageFileList.size());

            // 筛选需要的影像件类型：ID_HEAD、ID_NATION、ID_FACE
            List<LoanFile> targetImageFiles = imageFileList.stream()
                    .filter(file -> file.getFileType() == FileType.ID_HEAD ||
                            file.getFileType() == FileType.ID_NATION ||
                            file.getFileType() == FileType.ID_FACE)
                    .toList();

            if (targetImageFiles.isEmpty()) {
                logger.warn("HXBK影像件资料组装, 未找到需要的影像件类型(ID_HEAD/ID_NATION/ID_FACE)");
                return new HXBKMaterial[0];
            }

            HXBKMaterial[] materials = new HXBKMaterial[targetImageFiles.size()];

            // 处理每个影像件
            for (LoanFile imageFile : targetImageFiles) {
                // 获取OSS URL并拆分成文件名和路径
                String[] urlParts = getOssUrlAndSplit(imageFile);
                String fileName = urlParts[0];
                String filePath = urlParts[1];

                HXBKMaterial material = new HXBKMaterial();
                // 图片类型
                if ("credit".equals(type)) {
                    // 授信
                    material.setMType("2");
                } else {
                    // 用信
                    material.setMType("6");
                }
                // 文件名 - 只要文件名部分
                material.setMeterialName(fileName);
                // 完整路径
                material.setFilePath(filePath);

                // 根据文件类型设置对应的编码
                if (imageFile.getFileType() == FileType.ID_HEAD) {
                    // 身份证图片 - 身份证人脸面
                    material.setBigCode("20");
                    material.setSmallCode("201");
                } else if (imageFile.getFileType() == FileType.ID_NATION) {
                    // 身份证图片 - 身份证国徽面
                    material.setBigCode("20");
                    material.setSmallCode("202");
                } else if (imageFile.getFileType() == FileType.ID_FACE) {
                    // 人脸图片 - 活体人脸图片
                    material.setBigCode("26");
                    material.setSmallCode("212");
                }

                materials[targetImageFiles.indexOf(imageFile)] = material;
            }

            logger.info("HXBK影像件资料列表组装完成, materialCount: {}", materials.length);
            return materials;

        } catch (Exception e) {
            logger.error("HXBK影像件资料列表组装失败", e);
            throw new RuntimeException("影像件资料列表组装失败", e);
        }
    }

    /**
     * 获取OSS URL并拆分成文件名和路径
     *
     * @param loanFile 文件对象
     * @return 包含文件名和路径的数组，[0]为文件名, [1]为完整URL
     */
    private String[] getOssUrlAndSplit(LoanFile loanFile) {
        // 计算过期时间
        Date expireDate = Date.from(Instant.now().plusSeconds(hxbkConfig.getOssImageExpireSeconds()));
        String ossUrl = fileService.getOssUrl(loanFile.getOssBucket(), loanFile.getOssKey(), expireDate);

        // 先去除URL中的查询参数（问号后面的部分）
        String cleanUrl = ossUrl;
        int questionMarkIndex = ossUrl.indexOf('?');
        if (questionMarkIndex != -1) {
            cleanUrl = ossUrl.substring(0, questionMarkIndex);
        }

        // 拆分URL获取文件名和路径
        int lastSlashIndex = cleanUrl.lastIndexOf('/');
        // 文件名部分（已去除查询参数）
        String fileName = cleanUrl.substring(lastSlashIndex + 1);

        return new String[]{fileName, ossUrl};
    }

    /**
     * 删除目录及其内容
     *
     * @param directory 要删除的目录
     */
    private void deleteDirectory(Path directory) {
        try (Stream<Path> pathStream = Files.walk(directory)) {
            pathStream.sorted(Comparator.reverseOrder())
                    .forEach(path -> {
                        try {
                            Files.delete(path);
                        } catch (IOException e) {
                            logger.warn("删除文件失败: {}", path, e);
                        }
                    });
        } catch (IOException e) {
            logger.warn("删除临时目录失败: {}", directory, e);
        }
    }

    // 依赖注入
    @Autowired
    public void setHxbkConfig(HXBKConfig hxbkConfig) {
        this.hxbkConfig = hxbkConfig;
    }

    @Autowired
    public void setFileService(FileService fileService) {
        this.fileService = fileService;
    }

    @Autowired
    public void setHXBKAntFileLogRepository(HXBKAntFileLogRepository hxbkAntFileLogRepository) {
        this.hxbkAntFileLogRepository = hxbkAntFileLogRepository;
    }


    public void uploadOfflineRepayReturnFile(HXBKRepayReturnFileDTO dto, LocalDate actualRepayDate) throws Exception {

        String sftpPath = returnFilePrefix + actualRepayDate.format(DateTimeFormatter.BASIC_ISO_DATE) + "/";

        uploadStreamToSftp(dto.getCheckOutputStream(), dto.getCheckFileName(), sftpPath);

        uploadStreamToSftp(dto.getFileOutputStream(), dto.getFileName(), sftpPath);

    }


    public void uploadStreamToSftp(ByteArrayOutputStream stream, String fileName, String remoteDir) throws Exception {

        JSch jsch = new JSch();
        Session session = jsch.getSession(hxbkConfig.getSftpUsername(), hxbkConfig.getSftpHost(), hxbkConfig.getSftpPort());
        session.setPassword(hxbkConfig.getSftpPassword());

        Properties config = new Properties();
        config.put("StrictHostKeyChecking", "no");
        config.put("server_host_key", "ssh-rsa,ssh-dss");
        session.setConfig(config);

        session.connect();

        ChannelSftp sftp = (ChannelSftp) session.openChannel("sftp");
        sftp.connect();
        try {
            // 递归创建目录
            confirmPath(remoteDir, sftp);

            // 上传文件
            byte[] fileBytes = stream.toByteArray();
            InputStream inputStream = new ByteArrayInputStream(fileBytes);
            sftp.put(inputStream, fileName);

            logger.info("文件上传成功, 路径：{}/{}", remoteDir, fileName);

        } finally {
            if (null != stream) {
                stream.close();
            }

            sftp.disconnect();
            session.disconnect();
        }
    }

    private void confirmPath(String remoteDir, ChannelSftp sftp) throws SftpException {
        String[] folders = remoteDir.split("/");
        String path = "";
        for (String folder : folders) {
            if (folder.isEmpty()) {
                continue;
            }
            path += "/" + folder;
            try {
                sftp.cd(path);
            } catch (SftpException e) {
                sftp.mkdir(path);
                sftp.cd(path);
            }
        }
    }


}
