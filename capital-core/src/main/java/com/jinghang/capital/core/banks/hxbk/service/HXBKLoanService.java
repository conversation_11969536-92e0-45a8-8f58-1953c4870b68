package com.jinghang.capital.core.banks.hxbk.service;

import com.jinghang.capital.core.banks.AbstractBankLoanService;
import com.jinghang.capital.core.banks.hxbk.callback.dto.LoanApplyResultCallbackRequest;
import com.jinghang.capital.core.banks.hxbk.config.HXBKConfig;
import com.jinghang.capital.core.banks.hxbk.convert.HXBKLoanConvert;
import com.jinghang.capital.core.banks.hxbk.dto.bind.HXBKBindCardApplyRequest;
import com.jinghang.capital.core.banks.hxbk.dto.bind.HXBKBindCardApplyResponse;
import com.jinghang.capital.core.banks.hxbk.dto.credit.HXBKMaterial;
import com.jinghang.capital.core.banks.hxbk.dto.loan.*;
import com.jinghang.capital.core.banks.hxbk.enums.*;
import com.jinghang.capital.core.banks.hxbk.remote.HXBKRequestService;
import com.jinghang.capital.core.dto.ContractBizDto;
import com.jinghang.capital.core.entity.*;
import com.jinghang.capital.core.enums.*;
import com.jinghang.capital.core.exception.BizErrorCode;
import com.jinghang.capital.core.exception.BizException;
import com.jinghang.capital.core.repository.*;
import com.jinghang.capital.core.service.FileService;
import com.jinghang.capital.core.util.IdGenUtil;
import com.jinghang.capital.core.vo.loan.LoanApplyVo;
import com.jinghang.capital.core.vo.loan.LoanLprResultVo;
import com.jinghang.capital.core.vo.loan.LoanQueryVo;
import com.jinghang.capital.core.vo.loan.LoanResultVo;
import com.jinghang.capital.core.vo.repay.PlanItemVo;
import com.jinghang.capital.core.vo.repay.PlanVo;
import com.jinghang.capital.core.vo.repay.RepayDateApplyVo;
import com.jinghang.capital.core.vo.repay.RepayDateResultVo;
import com.jinghang.common.loan.PlanGenerator;
import com.jinghang.common.loan.plan.InterestType;
import com.jinghang.common.loan.plan.RepayPlan;
import com.jinghang.common.util.CollectionUtil;
import com.jinghang.common.util.JsonUtil;
import com.jinghang.common.util.StringUtil;
import org.apache.commons.lang.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2023/8/20
 */
@Service
@Qualifier("HXBKLoanService")
public class HXBKLoanService extends AbstractBankLoanService {

    private static final Logger logger = LoggerFactory.getLogger(HXBKLoanService.class);

    public static final int SIX = 6;
    public static final int SEVEN = 7;
    public static final int LOAN_STAGE_FILE_NUM = 1;
    private static final DateTimeFormatter VALID_FORMAT = DateTimeFormatter.ofPattern("HHmm");

    private HXBKRequestService requestService;

    private FileService fileService;

    private HXBKConfig hxbkConfig;

    private CreditRepository creditRepository;

    private HXBKCreditFlowRepository hxbkCreditFlowRepository;

    private HXBKBankRepository hxbkBankRepository;

    private AccountRepository accountRepository;

    private AccountBankCardRepository accountBankCardRepository;

    private AgreementSignatureRepository agreementSignatureRepository;

    private AccountContactInfoRepository accountContactInfoRepository;

    private static final int PERCENT = 100;

    private static final int TWELVE = 12;

    private static final BigDecimal CUSTOM_YEAR_DAYS = BigDecimal.valueOf(365);
    private static final int PROCESSING_POINT_NUM = 6;
    private LoanReplanRepository loanReplanRepository;
    private static final LocalDate ID_CARD_EXPIRE_PERMANENT = LocalDate.of(2099, 1, 1);
    private static final LocalDate HXBK_PERMANENT = LocalDate.of(2099, 12, 31);
    @Autowired
    private HXBKImageFileService hxbkImageFileService;
    @Autowired
    private LoanRepository loanRepository;

    @Override
    protected boolean bankLoanValidate(LoanApplyVo apply) {
//        LocalTime actTime = LocalTime.now();
//        if (!isValidTime(actTime)) {
//            throw new BizException(BizErrorCode.LOAN_TIME_INVALID);
//        }
        return true;
    }

//    private boolean isValidTime(LocalTime actTime) {
//        LocalTime start = LocalTime.parse(hxbkConfig.getLoanStartTime(), VALID_FORMAT);
//        LocalTime end = LocalTime.parse(hxbkConfig.getLoanEndTime(), VALID_FORMAT);
//        return actTime.isAfter(start) && actTime.isBefore(end);
//    }

    private String syncBankProtocol(AccountBankCard accountBankCard, Loan loan) {
        Credit credit = creditRepository.findById(loan.getCreditId()).orElseThrow();
        HXBKCreditFlow hxbkCreditFlow = hxbkCreditFlowRepository.findByCreditId(credit.getId()).orElseThrow();

        HXBKBindCardApplyRequest hxbkBindRequest = new HXBKBindCardApplyRequest();
        hxbkBindRequest.setOrderNo(IdGenUtil.genReqNo("GX"));// todo 这个GX 是否需要改
        hxbkBindRequest.setCardNo(accountBankCard.getCertNo());
        hxbkBindRequest.setChannelCode("");//todo 签约渠道填什么
        hxbkBindRequest.setOpenId(credit.getAccountId());// todo 资产方 用户唯一标识
        hxbkBindRequest.setCustomerNo(hxbkCreditFlow.getCustomNo());
        hxbkBindRequest.setCustomName(accountBankCard.getCardName());
        hxbkBindRequest.setMobile(accountBankCard.getPhone());
        hxbkBindRequest.setBankCardNo(accountBankCard.getCardNo());


        HXBKBindCardApplyResponse response = requestService.sign(hxbkBindRequest);


        if (!StringUtils.equals(HXBKResponseCode.OK.name(), response.getResultCode())) {
            getWarningService().warn("蚂蚁绑卡协议同步失败 code:" + response.getResultCode() + ",message: " + response.getResultMsg(), logger::error);
            throw new BizException(BizErrorCode.BANK_CODE_SIGN_APPLY.getCode(), "蚂蚁绑卡协议同步失败");
        }
        String signSeq = response.getBindSerialNo();
        return signSeq;
    }

    private HXBKLoanApplyRequest buildApplyRequest(Loan loan, AccountBankCard accountBankCard) {
        //委托保证合同的合同编号 loan保存
        String guaranteeContractNo = IdGenUtil.genReqNo("GCN");
        loan.setGuaranteeContractNo(guaranteeContractNo);
        Credit credit = creditRepository.findById(loan.getCreditId()).orElseThrow();
        String creditId = credit.getId();
        HXBKCreditFlow hxbkCreditFlow = hxbkCreditFlowRepository.findByCreditId(creditId).orElseThrow();
        HXBKLoanApplyRequest request = new HXBKLoanApplyRequest();
        request.setOrderNo(loan.getId());
        request.setOriginalOrderNo(creditId);
        request.setOpenId(credit.getAccountId());
        request.setCustomNo(hxbkCreditFlow.getCustomNo());
        request.setLoanAmount(credit.getLoanAmt());
        request.setPeriod(new BigDecimal(loan.getPeriods()));
        request.setCustomType(HXBKCustomType.NEW.getCode());
        request.setRepayType(HXBKRepayType.DEBX.getCode().toString());
        request.setChannelType(HXBKChannelType.APP.getCode().toString());
        request.setFundCode(hxbkConfig.getLoanFundCode());
        // 放款 银行卡相关
        List<HXBKBankList> hxbkBankList  = hxbkBankRepository.findByBankCode(accountBankCard.getBankCode());
        if (CollectionUtils.isEmpty(hxbkBankList) ){
            throw new BizException(BizErrorCode.BANK_CODE_SIGN_QUERY.getCode(), "绑定的放款卡所属银行不在支持银行列表,creditId:"+creditId+",loanId:"+loan.getId());
        }

        request.setBankCardNo(accountBankCard.getCardNo());
        request.setReservedMobile(accountBankCard.getPhone());
        // acctName 放款银行卡账户名
        request.setBankCardName(accountBankCard.getCardName());
        // bankCode 放款银行代码
        request.setBankCardAddress(accountBankCard.getBankCode());

        HXBKLoanWay loanPurpose = HXBKLoanWay.getEnumByLoanPurpose(credit.getFlowChannel(),loan.getLoanPurpose());
        request.setLoanWay(loanPurpose.getCode().toString());
        HXBKLoanApplyRequest.RiskData riskData = new HXBKLoanApplyRequest.RiskData();
        // 借款金额为10000元，则传参为 10000 00 分
        riskData.setLoanAmount(credit.getLoanAmt().multiply(BigDecimal.valueOf(100)).setScale(2, RoundingMode.HALF_UP));
        riskData.setCreditRate(BankChannel.HXBK.getIrrRate());
        request.setRiskData(riskData);

        List<LoanFile> imageFileList = getCommonService().getLoanFileRepository().findByCreditId(creditId);
        HXBKMaterial[] materials = hxbkImageFileService.buildImageMaterialList(imageFileList,"usecredit");
        request.setMaterials(materials);

        logger.info("蚂蚁 放款申请请求参数: {}", JsonUtil.toJsonString(request));
        return request;
    }


//    List<CYBKImageInfo> uploadFile(List<LoanFile> creditFiles, String idNo) {
//        List<CYBKImageInfo> imageInfoList = new ArrayList<>();
//        creditFiles.forEach(lf -> {
//            CYBKFileType fileType = CYBKFileType.getEnumByFileType(lf.getFileType());
//            if (Objects.nonNull(fileType)) {
//                CYBKImageInfo imageInfo = new CYBKImageInfo();
//                imageInfo.setImageStage("3"); //放款阶段
//                imageInfo.setImageName(fileType.getFileName());
//                imageInfo.setImageType(fileType.getCode());
//                String respNo = "";
//                try {
//                    logger.info("长银文件上传放款, 组装后参数: {}", JsonUtil.toJsonString(lf));
//                    respNo = cybkRequestService.sendImage(lf, idNo);
//                } catch (Exception e) {
//                    logger.error("长银文件上传放款信息异常：{}", e.getMessage());
//                }
//                imageInfo.setImageUrl(respNo);
//                imageInfoList.add(imageInfo);
//            }
//        });
//        return imageInfoList;
//    }

    /**
     * 证件有效转换
     *
     * @param expireDay
     * @return
     */
    public LocalDate calcCertValidDate(LocalDate expireDay) {
        if (ID_CARD_EXPIRE_PERMANENT.equals(expireDay)) {
            return HXBK_PERMANENT;
        }
        return expireDay;
    }

//    /**
//     * 每一期担保费：对客利率的等额本息还款金额-8.5%的等额本息还款金额；
//     *
//     * @param credit
//     * @return
//     */
//    public CYBKLoanGuaranteeInfo getGuaranteeAmtAndRate(CYBKLoanGuaranteeInfo guaranteeInfo, Credit credit) {
//        //计算 对客利率的等额本息还款金额
//        List<RepayPlan> repayPlanList = PlanGenerator.genPlan(InterestType.AVERAGE_PRINCIPAL_PLUS_INTEREST, LocalDate.now(),
//                credit.getLoanAmt(), credit.getCustomRate(), credit.getPeriods());
//        BigDecimal totalAmt = repayPlanList.stream().map(item -> item.getPrincipal().add(item.getInterest())).reduce(BigDecimal.ZERO, BigDecimal::add);
//        logger.info("长银直连, 放款申请生成对客还款计划: {}", JsonUtil.toJsonString(repayPlanList));
//
//        List<RepayPlan> repayBankPlanList = PlanGenerator.genPlan(InterestType.AVERAGE_PRINCIPAL_PLUS_INTEREST, LocalDate.now(),
//                credit.getLoanAmt(), credit.getBankRate(), credit.getPeriods());
//        BigDecimal totalBankAmt = repayBankPlanList.stream().map(item -> item.getPrincipal().add(item.getInterest())).reduce(BigDecimal.ZERO, BigDecimal::add);
//        logger.info("长银直连, 放款申请生成对资还款计划: {}", JsonUtil.toJsonString(repayBankPlanList));
//
//        BigDecimal guaranteeAmt = totalAmt.subtract(totalBankAmt); //担保费
//        guaranteeInfo.setGuarAmt(guaranteeAmt);
//
//        //担保总金额(12期) = 担保费总额 ÷ 实际期数 × 12
//        guaranteeAmt = guaranteeAmt.divide(new BigDecimal(credit.getPeriods()), SIX, RoundingMode.HALF_UP).multiply(new BigDecimal(TWELVE));
//
//        //担保费年费利率=担保费总额/申请金额
//        BigDecimal guarRate = guaranteeAmt.divide(credit.getLoanAmt(), PROCESSING_POINT_NUM, RoundingMode.HALF_UP); //平均担保费
//        guaranteeInfo.setGuarRate(guarRate);
//
//        //担保费计划
//        List<CYBKLoanGuaranteePlan> guaranteeList = new ArrayList<>();
//        repayPlanList.forEach(item -> {
//            RepayPlan bankRepayPlan = repayBankPlanList.stream().filter(bankItem ->
//                    bankItem.getCurrentPeriod() == item.getCurrentPeriod()).findFirst().orElseThrow();
//            CYBKLoanGuaranteePlan loanGuaranteePlan = new CYBKLoanGuaranteePlan();
//            loanGuaranteePlan.setPerdNo(String.valueOf(item.getCurrentPeriod()));
//            loanGuaranteePlan.setPerGuarFee(item.getPrincipal().add(item.getInterest())
//                    .subtract(bankRepayPlan.getPrincipal()).subtract(bankRepayPlan.getInterest()));
//            loanGuaranteePlan.setGuarDate(item.getRepayDate().format(DateTimeFormatter.ISO_LOCAL_DATE));
//            guaranteeList.add(loanGuaranteePlan);
//        });
//        guaranteeInfo.setGuaranteeList(guaranteeList);
//
//        return guaranteeInfo;
//    }

    @Override
    protected LoanResultVo bankLoanApply(Loan loan) {

        LoanResultVo loanResultVo = new LoanResultVo();
        //  调蚂蚁的文件都不用签了
//        Account account = accountRepository.findById(loan.getAccountId()).orElseThrow();
//        Credit credit = creditRepository.findById(loan.getCreditId()).orElseThrow();
//        if (null != credit.getFlowChannel() && FlowChannel.PPCJDL != credit.getFlowChannel()
//                && FlowChannel.LVXIN != credit.getFlowChannel()){
//            //授信阶段签章文件数
//            List<AgreementType> agrTypes = AgreementType.getAgreement(loan.getChannel(), LoanStage.LOAN, SignatureType.TEMPLATE);
//            //签章文件
//            List<AgreementSignature> agreementSignatures = fetchAgreementNeedFile(loan);
//            if (agreementSignatures.isEmpty()) {
//                for (AgreementType fileType : agrTypes) {
//                    AgreementSignature agreementSignature = generateAgrSignature(loan.getId(), fileType, SignatureType.TEMPLATE);
//                    agreementSignature.setBankMobilePhone(account.getMobile());
//                    agreementSignature.setIdentNo(account.getCertNo());
//                    agreementSignature.setPersonName(account.getName());
//                    agreementSignature.setTemplateNo(fileType.getTemplateNo()); //文件模板编号
//                    agreementSignature.setAddress(account.getLivingAddress());
//                    AgreementSignature saved = agreementSignatureRepository.save(agreementSignature);
//                    //签章申请监听
//                    getMqService().signatureApply(saved.getId());
//                }
//
//            }
//        }

        loanResultVo.setStatus(ProcessStatus.PROCESSING);
        return loanResultVo;
    }

    /**
     * 处理放款申请返回结果
     *
     * @param loan     借据
     * @param resultVo 返回结果
     */
    @Override
    public void bankLoanApplyResult(Loan loan, LoanResultVo resultVo) {
        ProcessStatus status = resultVo.getStatus();
        switch (status) {
            case FAIL -> {
                loan.setLoanStatus(LoanStatus.FAIL);
                loan.setFailMsg(resultVo.getFailMsg());
                getFinLoanService().updateLoan(loan);
            }
            case PROCESSING -> {
                loan.setLoanStatus(LoanStatus.PROCESSING);
                getFinLoanService().updateLoan(loan);

                sendContractUploadMessage(loan.getId());
                //
            }
            default -> {
            }
        }
    }

    @Override
    public void contractUpload(String loanId) {
        Loan loan = getCommonService().findLoanById(loanId);
        //放款阶段签章文件数
//        Credit credit = creditRepository.findById(loan.getCreditId()).orElse(new Credit());
        //  调湖消 都不用调签章服务
//        if (null != credit.getFlowChannel() && FlowChannel.PPCJDL != credit.getFlowChannel() && FlowChannel.LVXIN != credit.getFlowChannel()){
//            List<AgreementType> agrTypes = AgreementType.getAgreement(loan.getChannel(), LoanStage.LOAN, SignatureType.TEMPLATE);
//            List<FileType> fileTypes = agrTypes.stream().map(AgreementType::getFileType).toList();
//            List<LoanFile> loanFiles = fetchAllNeedFile(loan.getCreditId(), loanId, fileTypes);
//            logger.info("放款文件: {}", JSONObject.toJSONString(loanFiles));
//            if (loanFiles.size() != LOAN_STAGE_FILE_NUM) {
//                sendContractUploadDelayMessage(loanId);
//                logger.info("蚂蚁文件未签署完成: {}", JsonUtil.toJsonString(loanFiles));
//                return;
//            }
//            // 获取放款阶段需要上传的文件
//            List<LoanFile> stageFiles = getCommonService().getLoanFileRepository().findByRelatedId(loan.getId());
//            List<LoanFile> creditFiles = getCommonService().getLoanFileRepository().findByCreditIdAndFileType(loan.getCreditId(), FileType.ID_FACE);
//            stageFiles.add(creditFiles.get(0));
//        }


        AccountBankCard accountBankCard = accountBankCardRepository.findById(loan.getLoanCardId()).orElseThrow();
        // 共享扣款协议
//        String signSeq = syncBankProtocol(accountBankCard,loan);


        // 请求参数构建
        HXBKLoanApplyRequest request = buildApplyRequest(loan, accountBankCard);
        // 放款申请
        HXBKLoanApplyResponse response = requestService.loanApply(request);

        logger.info("蚂蚁放款申请响应参数: {}", JsonUtil.toJsonString(response));
        if(HXBKResponseCode.isNotOk(response.getResultCode())){
            loan.setLoanStatus(LoanStatus.FAIL);
            loan.setFailMsg(response.getResultMsg());
            loanRepository.save(loan);
            return;
        }
        getMqService().submitLoanResultQueryDelay(loan.getId());
    }

    private void sendContractUploadDelayMessage(String businessId) {
        // 监测放款阶段文件
        ContractBizDto contractBizDto = new ContractBizDto();
        contractBizDto.setStage(LoanStage.LOAN);
        contractBizDto.setBusinessId(businessId);
        getMqService().submitContractUploadDelay(JsonUtil.toJsonString(contractBizDto));
    }

    private void sendContractUploadMessage(String businessId) {
        // 监测放款阶段文件
        ContractBizDto contractBizDto = new ContractBizDto();
        contractBizDto.setStage(LoanStage.LOAN);
        contractBizDto.setBusinessId(businessId);
        getMqService().submitContractUpload(JsonUtil.toJsonString(contractBizDto));
    }

    @Override
    protected LoanResultVo bankLoanQuery(Loan loan, LoanQueryVo query) {
        // 放款结果查询
        HXBKLoanQueryRequest request = new HXBKLoanQueryRequest();
        request.setOrderNo(IdGenUtil.genReqNo("LQ"));
        request.setOriginalOrderNo(loan.getId());
        request.setProdType(HXBKProdType.XJD.getCode());

        HXBKLoanQueryResponse response = requestService.queryHXBKLoanQuery(request);

        if(HXBKResponseCode.isNotOk(response.getResultCode())){
            throw new BizException(BizErrorCode.LOAN_RESULT_QUERY_ERROR);
        }

        LoanResultVo applyResultVo = HXBKLoanConvert.INSTANCE.toVo(response);// todo 校验字段
        //更新借据信息
        HXBKCreditFlow creditFlow = hxbkCreditFlowRepository.findByCreditId(loan.getCreditId()).orElseThrow();
        applyResultVo.setFundingModel(creditFlow.getFundingModel());
        applyResultVo.setLoanNo(response.getReceiptInfo().getReceiptNo());
        // 蚂蚁的借据编号 更新到 creditFlow 表
        creditFlow.setLoanNo(response.getReceiptInfo().getReceiptNo());
        hxbkCreditFlowRepository.save(creditFlow);

        return applyResultVo;
    }

    @Override
    protected PlanVo getBankPlan(Loan loan) {
        // 查询蚂蚁还款计划接口
        HXBKRepayListQueryRequest request = new HXBKRepayListQueryRequest();
        request.setOrderNo(IdGenUtil.genReqNo("RLQ"));
        request.setOriginalOrderNo(loan.getId());
        HXBKRepayListQueryResponse response = requestService.queryHXBKRepayList(request);
        if (HXBKResponseCode.isNotOk(response.getResultCode())){
            throw new BizException(BizErrorCode.LOAN_RESULT_QUERY_ERROR);
        }

        return toPlanVo(loan, response);
    }

    private PlanVo toPlanVo(Loan loan, HXBKRepayListQueryResponse response) {

        PlanVo planVo = new PlanVo();
        planVo.setChannel(loan.getChannel());
        planVo.setLoanId(loan.getId());
        planVo.setLoanAmt(loan.getLoanAmt());
        planVo.setPeriods(loan.getPeriods());
        // 还款计划映射
        List<PlanItemVo> planItemVoList = HXBKLoanConvert.INSTANCE.toPlanVo(response.getRepayResultList());
        //对资金方不需要担保费
        //fillGuaranteeAmt(loan, planItemVoList); //担保费

        planItemVoList.forEach(p -> {
            p.setLoanId(planVo.getLoanId());
            p.setChannel(planVo.getChannel());
            BigDecimal totalAmt = Optional.ofNullable(p.getPrincipalAmt()).orElse(BigDecimal.ZERO)
                    .add(Optional.ofNullable(p.getInterestAmt()).orElse(BigDecimal.ZERO))
                    .add(Optional.ofNullable(p.getGuaranteeAmt()).orElse(BigDecimal.ZERO));

            p.setTotalAmt(totalAmt);
            p.setCustRepayStatus(RepayStatus.NORMAL);
            p.setBankRepayStatus(RepayStatus.NORMAL);
        });


        planVo.setPlanItems(planItemVoList);
        return planVo;
    }

    @Override
    public RepayDateResultVo appropriationInterest(RepayDateApplyVo applyVo) {
        return null;
    }

    @Override
    public LoanLprResultVo lpr(Credit credit) {
//        CYBKLprQueryRequest lprQueryRequest = new CYBKLprQueryRequest();
//        lprQueryRequest.setSignDate(credit.getPassTime().format(DateTimeFormatter.ISO_LOCAL_DATE));
//        lprQueryRequest.setPriceRate(credit.getBankRate());
//        CYBKLprQueryResponse response = requestService.request(lprQueryRequest,CYBKLprQueryResponse.class);

        LoanLprResultVo resultVo = new LoanLprResultVo();
//        resultVo.setLprRate(response.getLprRate().toPlainString());
//        resultVo.setLprDate(response.getLprDate());
//        // 资方利率(单位百分比)
//        resultVo.setBusinessRate(credit.getBankRate().multiply(BigDecimal.valueOf(PERCENT)).toString());

        return resultVo;
    }

    //查询签署完成协议文件
    private List<LoanFile> fetchAllNeedFile(String creditId, String loanId, List<FileType> fileTypes) {
        return getCommonService().getLoanFileRepository().findByCreditIdAndRelatedIdAndFileTypeList(creditId, loanId, LoanStage.LOAN.name(), fileTypes);
    }

    //放款前协议信息
    private List<AgreementSignature> fetchAgreementNeedFile(Loan loan) {
        List<AgreementType> agrTypes = AgreementType.getAgreement(loan.getChannel(), LoanStage.LOAN, SignatureType.TEMPLATE);
        List<FileType> fileTypes = new ArrayList<>(agrTypes.stream().map(AgreementType::getFileType).toList());
        return agreementSignatureRepository.findByChannelAndLoanStageAndFileTypeList(loan.getId(), BankChannel.HXBK, LoanStage.LOAN, fileTypes);
    }

    //签章信息
    private AgreementSignature generateAgrSignature(String loanId, AgreementType fileType, SignatureType signatureType) {
        AgreementSignature signature = new AgreementSignature();
        signature.setBusinessId(loanId);
        signature.setChannel(BankChannel.HXBK);
        signature.setFileType(fileType.getFileType());
        signature.setSignState(ProcessStatus.INIT);
        signature.setLoanStage(LoanStage.LOAN);
        signature.setSignatureType(signatureType);
        signature.setDynamicOssBucket("");
        signature.setDynamicOssKey("");
        return signature;
    }

    /**
     * 填充融担费用
     *
     * @param loan
     * @return
     */
    public void fillGuaranteeAmt(Loan loan, List<PlanItemVo> planItemVoList) {
        Credit credit = creditRepository.findById(loan.getCreditId()).orElseThrow();

        //计算 对客利率的等额本息还款金额
        List<RepayPlan> repayPlanList = PlanGenerator.genPlan(InterestType.AVERAGE_PRINCIPAL_PLUS_INTEREST, LocalDate.now(),
                loan.getLoanAmt(), credit.getCustomRate(), credit.getPeriods());

        Map<Integer, RepayPlan> repayMap = repayPlanList.stream()
                .collect(Collectors.toMap(RepayPlan::getCurrentPeriod, Function.identity(), (o1, o2) -> o2));

        planItemVoList.forEach(p -> {
            RepayPlan repayPlan = repayMap.get(p.getPeriod());
            p.setGuaranteeAmt(repayPlan.getPrincipal().add(repayPlan.getInterest()).subtract(p.getPrincipalAmt()).subtract(p.getInterestAmt()));
        });
    }


    /**
     * 客户风险等级计算
     * <p>
     * A:(400,1000] B:(300,400] C:(200,300] D:(0,200]
     *
     * @param acardScore
     * @return
     */
    private final int oneThousand = 1000;
    private final int fourHundred = 400;
    private final int threeHundred = 300;
    private final int twoHundred = 200;

    public String getCreditLevel(String acardScore) {
        if (StringUtil.isNotBlank(acardScore)) {
            int score = Integer.parseInt(acardScore);
            if (score <= oneThousand && score > fourHundred) {
                return "A";
            } else if (score <= fourHundred && score > threeHundred) {
                return "B";
            } else if (score <= threeHundred && score > twoHundred) {
                return "C";
            } else {
                return "D";
            }
        } else {
            return "D";
        }
    }

    @Override
    public boolean isSupport(BankChannel channel) {
        return BankChannel.HXBK == channel;
    }

    @Autowired
    public void setRequestService(HXBKRequestService requestService) {
        this.requestService = requestService;
    }

    @Autowired
    public void setFileService(FileService fileService) {
        this.fileService = fileService;
    }

    @Autowired
    public void setCreditRepository(CreditRepository creditRepository) {
        this.creditRepository = creditRepository;
    }

    @Autowired
    public void setHXBKCreditFlowRepository(HXBKCreditFlowRepository hxbkCreditFlowRepository) {
        this.hxbkCreditFlowRepository = hxbkCreditFlowRepository;
    }

    @Autowired
    public void setAccountRepository(AccountRepository accountRepository) {
        this.accountRepository = accountRepository;
    }

    @Autowired
    public void setAccountBankCardRepository(AccountBankCardRepository accountBankCardRepository) {
        this.accountBankCardRepository = accountBankCardRepository;
    }

    @Autowired
    public void setAgreementSignatureRepository(AgreementSignatureRepository agreementSignatureRepository) {
        this.agreementSignatureRepository = agreementSignatureRepository;
    }

    @Autowired

    public void setAccountContactInfoRepository(AccountContactInfoRepository accountContactInfoRepository) {
        this.accountContactInfoRepository = accountContactInfoRepository;
    }

    @Autowired
    public void setLoanReplanRepository(LoanReplanRepository loanReplanRepository) {
        this.loanReplanRepository = loanReplanRepository;
    }

    @Autowired
    public void setHxbkConfig(HXBKConfig hxbkConfig) {
        this.hxbkConfig = hxbkConfig;
    }

    @Autowired
    public void setHxbkBankRepository(HXBKBankRepository hxbkBankRepository) {
        this.hxbkBankRepository = hxbkBankRepository;
    }


    public void doLoanApplyResultCallback(LoanApplyResultCallbackRequest request){
        Loan loan = getCommonService().findLoanById(request.getOrderNo());
        // todo invoke HXBKMethod.LOAN_QUERY , compare status ,if not equal, warning.
        // 放款结果查询，和回调接口中的 status比较，加入
        HXBKLoanQueryRequest loanQueryRequest = new HXBKLoanQueryRequest();
        loanQueryRequest.setOrderNo(IdGenUtil.genReqNo("LQ"));
        loanQueryRequest.setOriginalOrderNo(loan.getId());
        loanQueryRequest.setProdType(HXBKProdType.XJD.getCode());

        HXBKLoanQueryResponse response = requestService.queryHXBKLoanQuery(loanQueryRequest);

        if(HXBKResponseCode.isOk(response.getResultCode())){
            // 比较结果
            if (!org.apache.commons.lang3.StringUtils.equalsIgnoreCase(response.getStatus(),request.getStatus())){
                logger.error("loanId:[{}]:用信回调接口的状态：{} 与 用信申请结果查询返回的状态: {} 不一致.",request.getOrderNo(),request.getStatus(),response.getStatus());
                getWarningService().warn("loanId:["+request.getOrderNo()+"]:用信回调接口的状态："+request.getStatus()+" 与 用信申请结果查询返回的状态:"+response.getStatus()+"不一致，");
                return;
            }
        }else if(HXBKResponseCode.isNotOk(response.getResultCode())){
            logger.error(response.getResultMsg());
            throw new RuntimeException("loanId:["+request.getOrderNo()+"],[用信申请回调接口] 发起主动查询用信结果异常："+response.getResultMsg());
        }


        if (loan.getLoanStatus().isFinal()) {
            return;
        }
        LoanResultVo loanResultVo = HXBKLoanConvert.INSTANCE.toVo(response);
        // 1 解析入库
        //更新借据信息
        HXBKCreditFlow creditFlow = hxbkCreditFlowRepository.findByCreditId(loan.getCreditId()).orElseThrow();
        loanResultVo.setFundingModel(creditFlow.getFundingModel());
        creditFlow.setLoanNo(request.getReceiptInfo().getReceiptNo());
        hxbkCreditFlowRepository.save(creditFlow);

        loan.setLoanNo(StringUtil.isNotBlank(loanResultVo.getLoanNo()) ? loanResultVo.getLoanNo() : loan.getLoanNo());
        loan.setLoanContractNo(StringUtil.isNotBlank(loanResultVo.getLoanContractNo()) ? loanResultVo.getLoanContractNo() : loan.getLoanContractNo());
        loan.setLoanChannel(loanResultVo.getLoanChannel());
        loan.setLoanStatus(ProcessStatus.SUCCESS.equals(loanResultVo.getStatus()) ? LoanStatus.SUCCESS : LoanStatus.FAIL);
        loan.setFailMsg(loanResultVo.getFailMsg());
        loan.setLoanTime(null != loanResultVo.getLoanTime() ? loanResultVo.getLoanTime() : loan.getLoanTime());
        loan.setFundingModel(loanResultVo.getFundingModel());
        if (ProcessStatus.SUCCESS.equals(loanResultVo.getStatus())) {

            // 生成还款计划
            logger.info("生成还款计划, loanId: {}", loanResultVo.getLoanId());
            PlanVo planVo = getBankPlan(loan);

            if (CollectionUtil.isEmpty(planVo.getPlanItems()) || !Objects.equals(loan.getPeriods(), planVo.getPlanItems().size())) {
                logger.error("还款计划条数与借款期数不一致，loanId: {}，period:{}, loanRepayPlan size:{}", loan.getId(), loan.getPeriods(),
                        planVo.getPlanItems().size());
                getWarningService().warn("还款计划条数与借款期数不一致, loanId:" + loan.getId());
                getMqService().submitLoanResultQueryDelay(loan.getId());
                return;
            }
            //保存更新到本地还款计划表 todo 验证湖消的入库数据对应是否正确
            List<LoanReplan> replanList = getFinLoanService().genRepayPlans(planVo);

        }

        getCommonService().saveLoan(loan);
    }
}
