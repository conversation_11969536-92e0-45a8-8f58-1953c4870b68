package com.jinghang.capital.core.banks.hxbk.service;


import com.jinghang.capital.core.banks.hxbk.config.HXBKConfig;
import com.jinghang.capital.core.util.AgeUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.time.temporal.ChronoUnit;
import java.util.ArrayList;
import java.util.Arrays;

/**
 * hxbk本地校验
 *
 * @Author: Lior
 * @CreateTime: 2025/7/11 9:06
 */
@Service
public class HXBKLocalValid {

    private static final LocalDate ID_CARD_EXPIRE_PERMANENT = LocalDate.of(2099, 1, 1);
    private static final LocalDate HXBK_PERMANENT = LocalDate.of(9999, 12, 31);

    private static final DateTimeFormatter VALID_FORMAT = DateTimeFormatter.ofPattern("HHmm");
    private static final DateTimeFormatter AGE_FORMAT = DateTimeFormatter.ofPattern("yyyyMMdd");

    private static final int ID_AGE_START = 6;
    private static final int ID_AGE_END = 14;


    private static final int AGE_LOWER_LIMIT = 22;
    private static final int AGE_HIGH_LIMIT = 55;


    private static final BigDecimal AMOUNT_LOWER_LIMIT = new BigDecimal("500");
    private static final BigDecimal AMOUNT_HIGH_LIMIT = new BigDecimal("50000");

    private static final ArrayList<Integer> PERIODS_RANGE = new ArrayList<>(Arrays.asList(3, 6, 9, 12));

    private static final int CERT_EXPIRE_DAYS_LIMIT = 30;

    @Autowired
    private HXBKConfig config;

    /**
     * 授信金额检验
     *
     * @param amount
     * @return
     */
    public boolean validCreditAmount(BigDecimal amount) {
        return AMOUNT_LOWER_LIMIT.compareTo(amount) <= 0 && AMOUNT_HIGH_LIMIT.compareTo(amount) >= 0;
    }

    /**
     * 申请期数校验
     *
     * @param periods
     * @return
     */
    public boolean validPeriods(Integer periods) {
        return PERIODS_RANGE.contains(periods);
    }


    /**
     * 判断年龄是否有效
     *
     * @param certNo 身份证号
     * @return 结果
     */
    public boolean isValidAge(String certNo) {
        String ageStr = certNo.substring(ID_AGE_START, ID_AGE_END);
        LocalDate birth = LocalDate.parse(ageStr, AGE_FORMAT);
        long age = AgeUtil.calcAge(birth);
        return age >= AGE_LOWER_LIMIT && age <= AGE_HIGH_LIMIT;
    }

    /**
     * 判断有效授信区域
     *
     * @param provinceCode 省代码
     * @return 结果
     */
    public boolean isValidDistrict(String provinceCode) {
        return !(provinceCode.endsWith("54")
                || provinceCode.equals("65")
                || provinceCode.equals("71")
                || provinceCode.equals("81")
                || provinceCode.equals("82"));
    }

    /**
     * 判断身份证有效期是否符合要求
     * 身份证有效期距离当前时间必须大于30天
     *
     * @param certValidEnd 身份证有效期结束日期
     * @return 结果
     */
    public boolean isValidCertExpiry(LocalDate certValidEnd) {
        if (certValidEnd == null) {
            return false;
        }

        // 如果是长期有效（2099-01-01或9999-12-31），直接返回true
        if (certValidEnd.equals(ID_CARD_EXPIRE_PERMANENT) || certValidEnd.equals(HXBK_PERMANENT)) {
            return true;
        }

        // 计算身份证有效期距离当前时间的天数
        LocalDate currentDate = LocalDate.now();
        long daysBetween = ChronoUnit.DAYS.between(currentDate, certValidEnd);

        // 身份证有效期距离当前时间必须大于30天
        return daysBetween > CERT_EXPIRE_DAYS_LIMIT;
    }
}
