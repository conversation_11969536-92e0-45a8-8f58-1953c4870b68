package com.jinghang.capital.core.config;

import com.jinghang.capital.core.service.MqService;
import org.springframework.amqp.core.Binding;
import org.springframework.amqp.core.BindingBuilder;
import org.springframework.amqp.core.Exchange;
import org.springframework.amqp.core.ExchangeBuilder;
import org.springframework.amqp.core.Queue;
import org.springframework.amqp.core.QueueBuilder;
import org.springframework.amqp.rabbit.config.ContainerCustomizer;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.amqp.rabbit.listener.DirectMessageListenerContainer;
import org.springframework.amqp.rabbit.listener.SimpleMessageListenerContainer;
import org.springframework.boot.autoconfigure.amqp.RabbitTemplateCustomizer;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

@Configuration
public class RabbitConfig {

    @Bean
    public ContainerCustomizer<SimpleMessageListenerContainer> rabbitTracingSimpleListenerContainerCustomizer() {
        return c -> c.setObservationEnabled(true);
    }

    @Bean
    public ContainerCustomizer<DirectMessageListenerContainer> rabbitTracingDirectistenerContainerCustomizer() {
        return c -> c.setObservationEnabled(true);
    }

    @Bean
    public RabbitTemplateCustomizer rabbitTemplateCustomizer() {
        return c -> c.setObservationEnabled(true);
    }


    /****
     * exchange
     * @return
     */
    @Bean
    public Exchange creditExchange() {
        return ExchangeBuilder.directExchange(Exchanges.CREDIT).build();
    }

    @Bean
    public Exchange loanExchange() {
        return ExchangeBuilder.directExchange(Exchanges.LOAN).build();
    }

    @Bean
    public Exchange repayExchange() {
        return ExchangeBuilder.directExchange(Exchanges.REPAY).build();
    }

    @Bean
    public Exchange signatureExchange() {
        return ExchangeBuilder.directExchange(Exchanges.SIGNATURE).build();
    }

    @Bean
    public Exchange contractExchange() {
        return ExchangeBuilder.directExchange(Exchanges.CONTRACT).build();
    }

    @Bean
    public Exchange trustExchange() {
        return ExchangeBuilder.directExchange(Exchanges.TRUST).build();
    }

    @Bean
    public Exchange covenantExchange() {
        return ExchangeBuilder.directExchange(Exchanges.COVENANT).build();
    }

    @Bean
    public Exchange quotaExchange() {
        return ExchangeBuilder.directExchange(Exchanges.QUOTA).build();
    }

    @Bean
    public Exchange claimExchange() {
        return ExchangeBuilder.directExchange(Exchanges.CLAIM).build();
    }

    @Bean
    public Exchange commonConfigExchange() {
        return ExchangeBuilder.directExchange(Exchanges.COMMON_CONFIG).build();
    }



    /****
     * queue
     * @return
     */
    @Bean
    public Queue creditQueryResultQueueDelay() {
        return QueueBuilder.durable(Queues.CREDIT_QUERY_DELAY)
            .withArgument("x-dead-letter-routing-key", RoutingKeys.CREDIT_QUERY)
            .withArgument("x-dead-letter-exchange", Exchanges.CREDIT)
            .build();
    }

    @Bean
    public Queue gmxtCreditQueryResultQueueDelay() {
        return QueueBuilder.durable(Queues.GMXT_CREDIT_QUERY_DELAY)
            .withArgument("x-dead-letter-routing-key", RoutingKeys.CREDIT_QUERY)
            .withArgument("x-dead-letter-exchange", Exchanges.CREDIT)
            .build();
    }

    @Bean
    public Queue creditQueryResultQueue() {
        return QueueBuilder.durable(Queues.CREDIT_QUERY).build();
    }

    @Bean
    public Queue creditStageUploadQueueDelay() {
        return QueueBuilder.durable(Queues.CREDIT_STAGE_UPLOAD_DELAY)
            .withArgument("x-dead-letter-routing-key", RoutingKeys.CREDIT_STAGE_UPLOAD)
            .withArgument("x-dead-letter-exchange", Exchanges.CREDIT)
            .build();
    }

    @Bean
    public Queue creditStageUploadQueue() {
        return QueueBuilder.durable(Queues.CREDIT_STAGE_UPLOAD).build();
    }


    @Bean
    public Queue creditFailedNotifyQueueDelay() {
        return QueueBuilder.durable(Queues.CREDIT_FAILED_NOTIFY_DELAY)
            .withArgument("x-dead-letter-routing-key", RoutingKeys.CREDIT_FAILED_NOTIFY)
            .withArgument("x-dead-letter-exchange", Exchanges.CREDIT)
            .build();
    }

    @Bean
    public Queue creditFailedNotifyQueue() {
        return QueueBuilder.durable(Queues.CREDIT_FAILED_NOTIFY).build();
    }


    @Bean
    public Queue creditAdjustResultQueueDelay() {
        return QueueBuilder.durable(Queues.CREDIT_ADJUST_QUERY_DELAY)
            .withArgument("x-dead-letter-routing-key", RoutingKeys.CREDIT_ADJUST_QUERY)
            .withArgument("x-dead-letter-exchange", Exchanges.CREDIT)
            .build();
    }

    @Bean
    public Queue creditAdjustQueryResultQueue() {
        return QueueBuilder.durable(Queues.CREDIT_ADJUST_QUERY).build();
    }


    @Bean
    public Queue creditNewAdjustQueryQueueDelay() {
        return QueueBuilder.durable(Queues.CREDIT_NEW_ADJUST_QUERY_DELAY)
            .withArgument("x-dead-letter-routing-key", RoutingKeys.CREDIT_NEW_ADJUST_QUERY)
            .withArgument("x-dead-letter-exchange", Exchanges.CREDIT)
            .build();
    }

    @Bean
    public Queue creditNewAdjustQueryQueue() {
        return QueueBuilder.durable(Queues.CREDIT_NEW_ADJUST_QUERY).build();
    }


    /**
     * 授信阶段协议重签
     *
     * @return
     */
    @Bean
    public Queue creditAgreementRecreditQueue() {
        return QueueBuilder.durable(Queues.CREDIT_AGREEMENT_RECREATE).build();
    }

    /**
     * 放款后阶段协议重签
     *
     * @return
     */
    @Bean
    public Queue loanAfterAgreementSignQueue() {
        return QueueBuilder.durable(Queues.LOAN_AFTER_AGREEMENT_SIGN).build();
    }

    @Bean
    public Queue loanAfterAgreementSignDelayQueue() {
        return QueueBuilder.durable(Queues.LOAN_AFTER_AGREEMENT_SIGN_DELAY)
            .withArgument("x-dead-letter-routing-key", RoutingKeys.LOAN_AFTER_AGREEMENT_SIGN)
            .withArgument("x-dead-letter-exchange", Exchanges.LOAN)
            .build();
    }


    @Bean
    public Queue gmxtUsingLettersQueryQueue() {
        return QueueBuilder.durable(Queues.GMXT_USING_LETTERS_QUERY).build();
    }

    @Bean
    public Queue gmxtUsingLettersQueryDelayQueue() {
        return QueueBuilder.durable(Queues.GMXT_USING_LETTERS_QUERY_DELAY)
            .withArgument("x-dead-letter-routing-key", RoutingKeys.GMXT_USING_LETTERS_QUERY)
            .withArgument("x-dead-letter-exchange", Exchanges.LOAN)
            .build();
    }


    @Bean
    public Queue zyebankBindChangeQueueDelay() {
        return QueueBuilder.durable(Queues.ZYEBANK_BIND_CHANGE_DELAY)
            .withArgument("x-dead-letter-routing-key", RoutingKeys.ZYEBANK_BIND_CHANGE)
            .withArgument("x-dead-letter-exchange", Exchanges.REPAY)
            .build();
    }

    @Bean
    public Queue zyebankBindChangeQueue() {
        return QueueBuilder.durable(Queues.ZYEBANK_BIND_CHANGE).build();
    }


    @Bean
    public Queue loanQueryResultQueueDelay() {
        return QueueBuilder.durable(Queues.LOAN_QUERY_DELAY)
            .withArgument("x-dead-letter-routing-key", RoutingKeys.LOAN_QUERY)
            .withArgument("x-dead-letter-exchange", Exchanges.LOAN)
            .build();
    }

    @Bean
    public Queue gmxtLoanQueryResultQueueDelay() {
        return QueueBuilder.durable(Queues.GMXT_LOAN_QUERY_DELAY)
            .withArgument("x-dead-letter-routing-key", RoutingKeys.LOAN_QUERY)
            .withArgument("x-dead-letter-exchange", Exchanges.LOAN)
            .build();
    }

    @Bean
    public Queue loanQueryResultQueue() {
        return QueueBuilder.durable(Queues.LOAN_QUERY).build();
    }


    @Bean
    public Queue loanApplyQueueDelay() {
        return QueueBuilder.durable(Queues.LOAN_APPLY_DELAY)
            .withArgument("x-dead-letter-routing-key", RoutingKeys.LOAN_APPLY)
            .withArgument("x-dead-letter-exchange", Exchanges.LOAN)
            .build();
    }

    @Bean
    public Queue loanApplyQueue() {
        return QueueBuilder.durable(Queues.LOAN_APPLY).build();
    }


    @Bean
    public Queue loanReplanQueryResultQueueDelay() {
        return QueueBuilder.durable(Queues.LOAN_REPLAN_QUERY_DELAY)
            .withArgument("x-dead-letter-routing-key", RoutingKeys.LOAN_REPLAN_QUERY)
            .withArgument("x-dead-letter-exchange", Exchanges.LOAN)
            .build();
    }

    @Bean
    public Queue loanReplanQueryResultQueue() {
        return QueueBuilder.durable(Queues.LOAN_REPLAN_QUERY).build();
    }

    @Bean
    public Queue loanReplanSyncQueueDelay() {
        return QueueBuilder.durable(Queues.LOAN_REPLAN_SYNC_DELAY)
            .withArgument("x-dead-letter-routing-key", RoutingKeys.LOAN_REPLAN_SYNC)
            .withArgument("x-dead-letter-exchange", Exchanges.LOAN)
            .build();
    }


    @Bean
    public Queue loanReplanSyncQueue() {
        return QueueBuilder.durable(Queues.LOAN_REPLAN_SYNC).build();
    }

    @Bean
    public Queue loanReplanSyncManualQueue() {
        return QueueBuilder.durable(Queues.LOAN_REPLAN_SYNC_MANUAL).build();
    }


    @Bean
    public Queue qinjiaFileUploadQueue() {
        return QueueBuilder.durable(Queues.QINJIA_FILE_UPLOAD).build();
    }

    @Bean
    public Queue qinjiaFileUploadQueueDelay() {
        return QueueBuilder.durable(Queues.QINJIA_FILE_UPLOAD_DELAY)
            .withArgument("x-dead-letter-routing-key", RoutingKeys.QINJIA_FILE_UPLOAD)
            .withArgument("x-dead-letter-exchange", Exchanges.LOAN)
            .build();
    }


    @Bean
    public Queue repayChargeQueryDelayQueue() {
        return QueueBuilder.durable(Queues.REPAY_QUERY_DELAY)
            .withArgument("x-dead-letter-routing-key", RoutingKeys.REPAY_QUERY)
            .withArgument("x-dead-letter-exchange", Exchanges.REPAY)
            .build();
    }

    @Bean
    public Queue repayChargeQueryQueue() {
        return QueueBuilder.durable(Queues.REPAY_QUERY).build();
    }


    @Bean
    public Queue claimApplyDelayQueue() {
        return QueueBuilder.durable(Queues.CLAIM_APPLY_DELAY)
            .withArgument("x-dead-letter-routing-key", RoutingKeys.CLAIM_APPLY)
            .withArgument("x-dead-letter-exchange", Exchanges.REPAY)
            .build();
    }

    @Bean
    public Queue claimApplyQueue() {
        return QueueBuilder.durable(Queues.CLAIM_APPLY).build();
    }


    @Bean
    public Queue claimQueryDelayQueue() {
        return QueueBuilder.durable(Queues.CLAIM_QUERY_DELAY)
            .withArgument("x-dead-letter-routing-key", RoutingKeys.CLAIM_QUERY)
            .withArgument("x-dead-letter-exchange", Exchanges.REPAY)
            .build();
    }

    @Bean
    public Queue claimQueryQueue() {
        return QueueBuilder.durable(Queues.CLAIM_QUERY).build();
    }


    @Bean
    public Queue flowClaimApplyDelayQueue() {
        return QueueBuilder.durable(Queues.FLOW_CLAIM_APPLY_DELAY)
            .withArgument("x-dead-letter-routing-key", RoutingKeys.FLOW_CLAIM_APPLY)
            .withArgument("x-dead-letter-exchange", Exchanges.CLAIM)
            .build();
    }

    @Bean
    public Queue flowClaimApplyQueue() {
        return QueueBuilder.durable(Queues.FLOW_CLAIM_APPLY).build();
    }


    @Bean
    public Queue flowClaimQueryDelayQueue() {
        return QueueBuilder.durable(Queues.FLOW_CLAIM_QUERY_DELAY)
            .withArgument("x-dead-letter-routing-key", RoutingKeys.FLOW_CLAIM_QUERY)
            .withArgument("x-dead-letter-exchange", Exchanges.CLAIM)
            .build();
    }

    @Bean
    public Queue flowClaimQueryQueue() {
        return QueueBuilder.durable(Queues.FLOW_CLAIM_QUERY).build();
    }

    @Bean
    public Queue loanUploadImageQueue() {
        return QueueBuilder.durable(Queues.LOAN_UPLOAD_IMAGE).build();
    }

    @Bean
    public Queue loanUploadImageDelayQueue() {
        return QueueBuilder.durable(Queues.LOAN_UPLOAD_IMAGE_DELAY)
            .withArgument("x-dead-letter-routing-key", RoutingKeys.LOAN_UPLOAD_IMAGE)
            .withArgument("x-dead-letter-exchange", Exchanges.LOAN)
            .build();
    }

    @Bean
    public Queue repayDeductQueryQueue() {
        return QueueBuilder.durable(Queues.REPAY_DEDUCT_QUERY).build();
    }

    @Bean
    public Queue claimAfterNotifyQueue() {
        return QueueBuilder.durable(Queues.CLAIM_AFTER_NOTIFY).build();
    }

    @Bean
    public Queue claimAfterNotifyDelayQueue() {
        return QueueBuilder.durable(Queues.CLAIM_AFTER_NOTIFY_DELAY)
            .withArgument("x-dead-letter-routing-key", RoutingKeys.CLAIM_AFTER_NOTIFY)
            .withArgument("x-dead-letter-exchange", Exchanges.REPAY)
            .build();
    }

    @Bean
    public Queue signatureQueryDelayQueue() {
        return QueueBuilder.durable(Queues.SIGNATURE_QUERY_DELAY)
            .withArgument("x-dead-letter-routing-key", RoutingKeys.SIGNATURE_QUERY)
            .withArgument("x-dead-letter-exchange", Exchanges.SIGNATURE)
            .build();
    }

    @Bean
    public Queue signatureQueryQueue() {
        return QueueBuilder.durable(Queues.SIGNATURE_QUERY).build();
    }


    @Bean
    public Queue signatureNewQueryDelayQueue() {
        return QueueBuilder.durable(Queues.SIGNATURE_NEW_QUERY_DELAY)
            .withArgument("x-dead-letter-routing-key", RoutingKeys.SIGNATURE_NEW_QUERY)
            .withArgument("x-dead-letter-exchange", Exchanges.SIGNATURE)
            .build();
    }

    @Bean
    public Queue signatureNewQueryQueue() {
        return QueueBuilder.durable(Queues.SIGNATURE_NEW_QUERY).build();
    }


    @Bean
    public Queue signatureApplyDelayQueue() {
        return QueueBuilder.durable(Queues.SIGNATURE_APPLY_DELAY)
            .withArgument("x-dead-letter-routing-key", RoutingKeys.SIGNATURE_APPLY)
            .withArgument("x-dead-letter-exchange", Exchanges.SIGNATURE)
            .build();
    }

    /**
     * 定义重签签章消息入口
     * @return
     */
    @Bean
    public Queue signatureCustomApplyAgainQueue() {
        return QueueBuilder.durable(Queues.SIGNATURE_CUSTOM_AGAIN_APPLY).build();
    }

    @Bean
    public Queue signatureApplyQueue() {
        return QueueBuilder.durable(Queues.SIGNATURE_APPLY).build();
    }


    @Bean
    public Queue contractDownloadQueue() {
        return QueueBuilder.durable(Queues.CONTRACT_DOWNLOAD).build();
    }

    @Bean
    public Queue contractDownloadDelayQueue() {
        return QueueBuilder.durable(Queues.CONTRACT_DOWNLOAD_DELAY)
            .withArgument("x-dead-letter-exchange", Exchanges.CONTRACT)
            .withArgument("x-dead-letter-routing-key", RoutingKeys.CONTRACT_DOWNLOAD)
            .build();
    }

    @Bean
    public Queue gmxtContractDownloadDelayQueue() {
        return QueueBuilder.durable(Queues.GMXT_CONTRACT_DOWNLOAD_DELAY)
            .withArgument("x-dead-letter-exchange", Exchanges.CONTRACT)
            .withArgument("x-dead-letter-routing-key", RoutingKeys.CONTRACT_DOWNLOAD)
            .build();
    }


    @Bean
    public Queue contractUploadQueue() {
        return QueueBuilder.durable(Queues.CONTRACT_UPLOAD).build();
    }

    @Bean
    public Queue contractUploadDelayQueue() {
        return QueueBuilder.durable(Queues.CONTRACT_UPLOAD_DELAY)
            .withArgument("x-dead-letter-exchange", Exchanges.CONTRACT)
            .withArgument("x-dead-letter-routing-key", RoutingKeys.CONTRACT_UPLOAD)
            .build();
    }


    @Bean
    public Queue gmxtContractUploadDelayQueue() {
        return QueueBuilder.durable(Queues.GMXT_CONTRACT_UPLOAD_DELAY)
            .withArgument("x-dead-letter-exchange", Exchanges.CONTRACT)
            .withArgument("x-dead-letter-routing-key", RoutingKeys.CONTRACT_UPLOAD)
            .build();
    }

    @Bean
    public Queue bankSignQueryQueue() {
        return QueueBuilder.durable(Queues.BANK_SIGN_QUERY).build();
    }

    @Bean
    public Queue bankSignQueryDelayQueue() {
        return QueueBuilder.durable(Queues.BANK_SIGN_QUERY_DELAY)
            .withArgument("x-dead-letter-exchange", Exchanges.CONTRACT)
            .withArgument("x-dead-letter-routing-key", RoutingKeys.BANK_SIGN_QUERY)
            .build();
    }

    @Bean
    public Queue repayDeductQueryDelayQueue() {
        return QueueBuilder.durable(Queues.REPAY_DEDUCT_QUERY_DELAY)
            .withArgument("x-dead-letter-routing-key", RoutingKeys.REPAY_DEDUCT_QUERY)
            .withArgument("x-dead-letter-exchange", Exchanges.REPAY)
            .build();
    }

    @Bean
    public Queue repayApplyQueue() {
        return QueueBuilder.durable(Queues.REPAY_APPLY).build();
    }


    @Bean
    public Queue gmxtTrustRepayApplyQueue() {
        return QueueBuilder.durable(Queues.GMXT_TRUST_REPAY_APPLY).build();
    }

    @Bean
    public Queue gmxtTrustRepayQueryQueue() {
        return QueueBuilder.durable(Queues.GMXT_TRUST_REPAY_QUERY).build();
    }

    @Bean
    public Queue gmxtTrustRepayQueryDelayQueue() {
        return QueueBuilder.durable(Queues.GMXT_TRUST_REPAY_QUERY_DELAY)
            .withArgument("x-dead-letter-routing-key", RoutingKeys.GMXT_TRUST_REPAY_QUERY)
            .withArgument("x-dead-letter-exchange", Exchanges.REPAY)
            .build();
    }


    @Bean
    public Queue batchRepayApplyQueue() {
        return QueueBuilder.durable(Queues.BATCH_REPAY_APPLY).build();
    }

    @Bean
    public Queue batchRepayQueryQueue() {
        return QueueBuilder.durable(Queues.BATCH_REPAY_QUERY).build();
    }

    @Bean
    public Queue batchRepayQueryDelayQueue() {
        return QueueBuilder.durable(Queues.BATCH_REPAY_QUERY_DELAY)
            .withArgument("x-dead-letter-routing-key", RoutingKeys.BATCH_REPAY_QUERY)
            .withArgument("x-dead-letter-exchange", Exchanges.REPAY)
            .build();
    }

    @Bean
    public Queue fileUploadNotifyDelayQueue() {
        return QueueBuilder.durable(Queues.FILE_UPLOAD_NOTIFY_DELAY)
            .withArgument("x-dead-letter-routing-key", RoutingKeys.FILE_UPLOAD_NOTIFY)
            .withArgument("x-dead-letter-exchange", Exchanges.LOAN)
            .build();
    }

    @Bean
    public Queue fileUploadNotifyQueue() {
        return QueueBuilder.durable(Queues.FILE_UPLOAD_NOTIFY).build();
    }

    @Bean
    public Queue repayBankNotifyDelayQueue() {
        return QueueBuilder.durable(Queues.REPAY_BANK_NOTIFY_DELAY_QUEUE)
            .withArgument("x-dead-letter-routing-key", RoutingKeys.REPAY_BANK_NOTIFY_RK)
            .withArgument("x-dead-letter-exchange", Exchanges.REPAY)
            .build();
    }


    @Bean
    public Queue repayBankNotifyQueue() {
        return QueueBuilder.durable(Queues.REPAY_BANK_NOTIFY_QUEUE).build();
    }

    @Bean
    public Queue substituteBankApplyQueue() {
        return QueueBuilder.durable(Queues.SUBSTITUTE_BANK_APPLY_QUEUE).build();
    }

    @Bean
    public Queue substituteBankApplyDelayQueue() {
        return QueueBuilder.durable(Queues.SUBSTITUTE_BANK_APPLY_DELAY_QUEUE)
            .withArgument("x-dead-letter-routing-key", RoutingKeys.SUBSTITUTE_BANK_APPLY_RK)
            .withArgument("x-dead-letter-exchange", Exchanges.REPAY)
            .build();
    }

    @Bean
    public Queue repayBankBatchNotifyQueue() {
        return QueueBuilder.durable(Queues.REPAY_BANK_BATCH_NOTIFY_QUEUE).build();
    }


    @Bean
    public Queue repayCustomRetry() {
        return QueueBuilder.durable(Queues.REPAY_RETRY_QUEUE).build();
    }

    @Bean
    public Queue repaySubstituteApply() {
        return QueueBuilder.durable(Queues.REPAY_SUBSTITUTE_APPLY).build();
    }


    @Bean
    public Queue trustPlanChangeDelay() {
        return QueueBuilder.durable(Queues.TRUST_PLAN_CHANGE_DELAY)
            .withArgument("x-dead-letter-routing-key", RoutingKeys.TRUST_PLAN_CHANGE)
            .withArgument("x-dead-letter-exchange", Exchanges.TRUST)
            .build();
    }

    @Bean
    public Queue trustPlanChange() {
        return QueueBuilder.durable(Queues.TRUST_PLAN_CHANGE).build();
    }

    @Bean
    public Queue defrayQueryQueue() {
        return QueueBuilder.durable(Queues.DEFRAY_QUERY).build();
    }

    @Bean
    public Queue defrayQueryQueueDelayQueue() {
        return QueueBuilder.durable(Queues.DEFRAY_QUERY_DELAY)
            .withArgument("x-dead-letter-routing-key", RoutingKeys.DEFRAY_QUERY)
            .withArgument("x-dead-letter-exchange", Exchanges.REPAY)
            .build();
    }

    @Bean
    public Queue covenantDownloadLoanContractChange() {
        return QueueBuilder.durable(Queues.COVENANT_DOWNLOAD_LOAN_CONTRACT).build();
    }

    @Bean
    public Queue covenantDownloadLoanVoucherChange() {
        return QueueBuilder.durable(Queues.COVENANT_DOWNLOAD_LOAN_VOUCHER).build();
    }

    @Bean
    public Queue covenantDownloadOtherChange() {
        return QueueBuilder.durable(Queues.COVENANT_DOWNLOAD_OTHER).build();
    }

    @Bean
    public Queue covenantDownloadAgendaChange() {
        return QueueBuilder.durable(Queues.COVENANT_DOWNLOAD_AGENDA).build();
    }

    @Bean
    public Queue covenantDownloadAsyncApplyChange() {
        return QueueBuilder.durable(Queues.COVENANT_DOWNLOAD_ASYNC_APPLY).build();
    }

    @Bean
    public Queue covenantDownloadAsyncQueryChange() {
        return QueueBuilder.durable(Queues.COVENANT_DOWNLOAD_ASYNC_QUERY).build();
    }

    @Bean
    public Queue covenantDownloadAsyncQueryDelayChange() {
        return QueueBuilder.durable(Queues.COVENANT_DOWNLOAD_ASYNC_QUERY_DELAY)
            .withArgument("x-dead-letter-routing-key", RoutingKeys.COVENANT_DOWNLOAD_ASYNC_QUERY)
            .withArgument("x-dead-letter-exchange", Exchanges.COVENANT)
            .build();
    }

    @Bean
    public Queue quotaQueryQueue() {
        return QueueBuilder.durable(Queues.QUOTA_QUERY).build();
    }

    @Bean
    public Queue quotaQueryDelayQueue() {
        return QueueBuilder.durable(Queues.QUOTA_QUERY_DELAY)
            .withArgument("x-dead-letter-routing-key", RoutingKeys.QUOTA_QUERY)
            .withArgument("x-dead-letter-exchange", Exchanges.QUOTA)
            .build();
    }

    @Bean
    public Queue quotaAdjustApplyQueue() {
        return QueueBuilder.durable(Queues.QUOTA_ADJUST_APPLY).build();
    }

    @Bean
    public Queue quotaAdjustApplyDelayQueue() {
        return QueueBuilder.durable(Queues.QUOTA_ADJUST_APPLY_DELAY)
            .withArgument("x-dead-letter-routing-key", RoutingKeys.QUOTA_ADJUST_APPLY)
            .withArgument("x-dead-letter-exchange", Exchanges.QUOTA)
            .build();
    }

    @Bean
    public Queue quotaAdjustResultQueryQueue() {
        return QueueBuilder.durable(Queues.QUOTA_ADJUST_RESULT_QUERY).build();
    }

    @Bean
    public Queue quotaAdjustResultQueryDelayQueue() {
        return QueueBuilder.durable(Queues.QUOTA_ADJUST_RESULT_QUERY_DELAY)
            .withArgument("x-dead-letter-routing-key", RoutingKeys.QUOTA_ADJUST_RESULT_QUERY)
            .withArgument("x-dead-letter-exchange", Exchanges.QUOTA)
            .build();
    }

    @Bean
    public Queue quotaAdjustResultCallbackQueue() {
        return QueueBuilder.durable(Queues.QUOTA_ADJUST_RESULT_CALLBACK).build();
    }

    @Bean
    public Queue userInfoUpdateApplyQueue() {
        return QueueBuilder.durable(Queues.USER_INFO_UPDATE_APPLY).build();
    }

    @Bean
    public Queue userInfoUpdateQueryQueue() {
        return QueueBuilder.durable(Queues.USER_INFO_UPDATE_QUERY).build();
    }

    @Bean
    public Queue userInfoUpdateQueryDelayQueue() {
        return QueueBuilder.durable(Queues.USER_INFO_UPDATE_QUERY_DELAY)
            .withArgument("x-dead-letter-routing-key", RoutingKeys.USER_INFO_UPDATE_QUERY)
            .withArgument("x-dead-letter-exchange", Exchanges.CREDIT)
            .build();
    }

    @Bean
    public Queue trustFileSignQueryQueue() {
        return QueueBuilder.durable(Queues.TRUST_FILE_SIGN_QUERY).build();
    }

    @Bean
    public Queue trustFileSignQueryQueueDelayQueue() {
        return QueueBuilder.durable(Queues.TRUST_FILE_SIGN_QUERY_DELAY)
            .withArgument("x-dead-letter-routing-key", RoutingKeys.TRUST_FILE_SIGN_QUERY)
            .withArgument("x-dead-letter-exchange", Exchanges.TRUST)
            .build();
    }

    @Bean
    public Queue fpdSubstituteQueue() {
        return QueueBuilder.durable(Queues.FPD_SUBSTITUTE).build();
    }

    @Bean
    public Queue limiterRateUpdateQueue() {
        return QueueBuilder.durable(Queues.LIMITER_RATE_UPDATE).quorum().build();
    }


    /****
     * binding
     * @return
     */
    @Bean
    public Binding creditQueryResultDelayBinding(Exchange creditExchange, Queue creditQueryResultQueueDelay) {
        return BindingBuilder.bind(creditQueryResultQueueDelay).to(creditExchange).with(RoutingKeys.CREDIT_QUERY_DELAY).noargs();
    }

    @Bean
    public Binding gmxtCreditQueryResultDelayBinding(Exchange creditExchange, Queue gmxtCreditQueryResultQueueDelay) {
        return BindingBuilder.bind(gmxtCreditQueryResultQueueDelay).to(creditExchange).with(RoutingKeys.GMXT_CREDIT_QUERY_DELAY).noargs();
    }

    @Bean
    public Binding creditQueryResultBinding(Exchange creditExchange, Queue creditQueryResultQueue) {
        return BindingBuilder.bind(creditQueryResultQueue).to(creditExchange).with(RoutingKeys.CREDIT_QUERY).noargs();
    }


    @Bean
    public Binding creditAdjustResultQueueDelayBinding(Exchange creditExchange, Queue creditAdjustResultQueueDelay) {
        return BindingBuilder.bind(creditAdjustResultQueueDelay).to(creditExchange).with(RoutingKeys.CREDIT_ADJUST_QUERY_DELAY).noargs();
    }

    @Bean
    public Binding creditAdjustResultBinding(Exchange creditExchange, Queue creditAdjustQueryResultQueue) {
        return BindingBuilder.bind(creditAdjustQueryResultQueue).to(creditExchange).with(RoutingKeys.CREDIT_ADJUST_QUERY).noargs();
    }


    @Bean
    public Binding creditNewAdjustQueryDelayBinding(Exchange creditExchange, Queue creditNewAdjustQueryQueueDelay) {
        return BindingBuilder.bind(creditNewAdjustQueryQueueDelay).to(creditExchange).with(RoutingKeys.CREDIT_NEW_ADJUST_QUERY_DELAY).noargs();
    }

    @Bean
    public Binding creditNewAdjustQueryBinding(Exchange creditExchange, Queue creditNewAdjustQueryQueue) {
        return BindingBuilder.bind(creditNewAdjustQueryQueue).to(creditExchange).with(RoutingKeys.CREDIT_NEW_ADJUST_QUERY).noargs();
    }


    @Bean
    public Binding creditStageUploadDelayBinding(Exchange creditExchange, Queue creditStageUploadQueueDelay) {
        return BindingBuilder.bind(creditStageUploadQueueDelay).to(creditExchange).with(RoutingKeys.CREDIT_STAGE_UPLOAD_DELAY).noargs();
    }

    @Bean
    public Binding creditStageUploadBinding(Exchange creditExchange, Queue creditStageUploadQueue) {
        return BindingBuilder.bind(creditStageUploadQueue).to(creditExchange).with(RoutingKeys.CREDIT_STAGE_UPLOAD).noargs();
    }

    @Bean
    public Binding creditFailedNotifyDelayBinding(Exchange creditExchange, Queue creditFailedNotifyQueueDelay) {
        return BindingBuilder.bind(creditFailedNotifyQueueDelay).to(creditExchange).with(RoutingKeys.CREDIT_FAILED_NOTIFY_DELAY).noargs();
    }

    @Bean
    public Binding creditFailedNotifyBinding(Exchange creditExchange, Queue creditFailedNotifyQueue) {
        return BindingBuilder.bind(creditFailedNotifyQueue).to(creditExchange).with(RoutingKeys.CREDIT_FAILED_NOTIFY).noargs();
    }

    @Bean
    public Binding gmxtUsingLettersQueryBinding(Exchange loanExchange, Queue gmxtUsingLettersQueryQueue) {
        return BindingBuilder.bind(gmxtUsingLettersQueryQueue).to(loanExchange).with(RoutingKeys.GMXT_USING_LETTERS_QUERY).noargs();
    }

    @Bean
    public Binding gmxtUsingLettersQueryDelayBinding(Exchange loanExchange, Queue gmxtUsingLettersQueryDelayQueue) {
        return BindingBuilder.bind(gmxtUsingLettersQueryDelayQueue).to(loanExchange).with(RoutingKeys.GMXT_USING_LETTERS_QUERY_DELAY).noargs();
    }

    @Bean
    public Binding loanQueryResultDelayBinding(Exchange loanExchange, Queue loanQueryResultQueueDelay) {
        return BindingBuilder.bind(loanQueryResultQueueDelay).to(loanExchange).with(RoutingKeys.LOAN_QUERY_DELAY).noargs();
    }

    @Bean
    public Binding gmxtLoanQueryResultDelayBinding(Exchange loanExchange, Queue gmxtLoanQueryResultQueueDelay) {
        return BindingBuilder.bind(gmxtLoanQueryResultQueueDelay).to(loanExchange).with(RoutingKeys.GMXT_LOAN_QUERY_DELAY).noargs();
    }

    @Bean
    public Binding loanQueryResultBinding(Exchange loanExchange, Queue loanQueryResultQueue) {
        return BindingBuilder.bind(loanQueryResultQueue).to(loanExchange).with(RoutingKeys.LOAN_QUERY).noargs();
    }

    @Bean
    public Binding loanApplyDelayBinding(Exchange loanExchange, Queue loanApplyQueueDelay) {
        return BindingBuilder.bind(loanApplyQueueDelay).to(loanExchange).with(RoutingKeys.LOAN_APPLY_DELAY).noargs();
    }

    @Bean
    public Binding loanApplyBinding(Exchange loanExchange, Queue loanApplyQueue) {
        return BindingBuilder.bind(loanApplyQueue).to(loanExchange).with(RoutingKeys.LOAN_APPLY).noargs();
    }

    @Bean
    public Binding loanReplanQueryResultDelayBinding(Exchange loanExchange, Queue loanReplanQueryResultQueueDelay) {
        return BindingBuilder.bind(loanReplanQueryResultQueueDelay).to(loanExchange).with(RoutingKeys.LOAN_REPLAN_QUERY_DELAY).noargs();
    }

    @Bean
    public Binding loanReplanQueryResultBinding(Exchange loanExchange, Queue loanReplanQueryResultQueue) {
        return BindingBuilder.bind(loanReplanQueryResultQueue).to(loanExchange).with(RoutingKeys.LOAN_REPLAN_QUERY).noargs();
    }

    @Bean
    public Binding loanReplanSyncDelayBinding(Exchange loanExchange, Queue loanReplanSyncQueueDelay) {
        return BindingBuilder.bind(loanReplanSyncQueueDelay).to(loanExchange).with(RoutingKeys.LOAN_REPLAN_SYNC_DELAY).noargs();
    }

    @Bean
    public Binding loanReplanSyncBinding(Exchange loanExchange, Queue loanReplanSyncQueue) {
        return BindingBuilder.bind(loanReplanSyncQueue).to(loanExchange).with(RoutingKeys.LOAN_REPLAN_SYNC).noargs();
    }

    @Bean
    public Binding qinjiaFileUploadDelayBinding(Exchange loanExchange, Queue qinjiaFileUploadQueueDelay) {
        return BindingBuilder.bind(qinjiaFileUploadQueueDelay).to(loanExchange).with(RoutingKeys.QINJIA_FILE_UPLOAD_DELAY).noargs();
    }

    @Bean
    public Binding qinjiaFileUploadBinding(Exchange loanExchange, Queue qinjiaFileUploadQueue) {
        return BindingBuilder.bind(qinjiaFileUploadQueue).to(loanExchange).with(RoutingKeys.QINJIA_FILE_UPLOAD).noargs();
    }

    @Bean
    public Binding zyebankBindChangeBinding(Exchange repayExchange, Queue zyebankBindChangeQueue) {
        return BindingBuilder.bind(zyebankBindChangeQueue).to(repayExchange).with(RoutingKeys.ZYEBANK_BIND_CHANGE).noargs();
    }


    @Bean
    public Binding zyebankBindChangeDelayBinding(Exchange repayExchange, Queue zyebankBindChangeQueueDelay) {
        return BindingBuilder.bind(zyebankBindChangeQueueDelay).to(repayExchange).with(RoutingKeys.ZYEBANK_BIND_CHANGE_DELAY).noargs();
    }

    @Bean
    public Binding repayQueryDelayBinding(Exchange repayExchange, Queue repayChargeQueryDelayQueue) {
        return BindingBuilder.bind(repayChargeQueryDelayQueue).to(repayExchange).with(RoutingKeys.REPAY_QUERY_DELAY).noargs();
    }

    @Bean
    public Binding repayQueryBinding(Exchange repayExchange, Queue repayChargeQueryQueue) {
        return BindingBuilder.bind(repayChargeQueryQueue).to(repayExchange).with(RoutingKeys.REPAY_QUERY).noargs();
    }


    @Bean
    public Binding claimApplyDelayBinding(Exchange repayExchange, Queue claimApplyDelayQueue) {
        return BindingBuilder.bind(claimApplyDelayQueue).to(repayExchange).with(RoutingKeys.CLAIM_APPLY_DELAY).noargs();
    }

    @Bean
    public Binding claimApplyBinding(Exchange repayExchange, Queue claimApplyQueue) {
        return BindingBuilder.bind(claimApplyQueue).to(repayExchange).with(RoutingKeys.CLAIM_APPLY).noargs();
    }

    @Bean
    public Binding claimQueueDelayBinding(Exchange repayExchange, Queue claimQueryDelayQueue) {
        return BindingBuilder.bind(claimQueryDelayQueue).to(repayExchange).with(RoutingKeys.CLAIM_QUERY_DELAY).noargs();
    }

    @Bean
    public Binding claimQueueBinding(Exchange repayExchange, Queue claimQueryQueue) {
        return BindingBuilder.bind(claimQueryQueue).to(repayExchange).with(RoutingKeys.CLAIM_QUERY).noargs();
    }



    @Bean
    public Binding flowClaimApplyDelayBinding(Exchange claimExchange, Queue flowClaimApplyDelayQueue) {
        return BindingBuilder.bind(flowClaimApplyDelayQueue).to(claimExchange).with(RoutingKeys.FLOW_CLAIM_APPLY_DELAY).noargs();
    }

    @Bean
    public Binding flowClaimApplyBinding(Exchange claimExchange, Queue flowClaimApplyQueue) {
        return BindingBuilder.bind(flowClaimApplyQueue).to(claimExchange).with(RoutingKeys.FLOW_CLAIM_APPLY).noargs();
    }

    @Bean
    public Binding flowClaimQueueDelayBinding(Exchange claimExchange, Queue flowClaimQueryDelayQueue) {
        return BindingBuilder.bind(flowClaimQueryDelayQueue).to(claimExchange).with(RoutingKeys.FLOW_CLAIM_QUERY_DELAY).noargs();
    }

    @Bean
    public Binding flowClaimQueueBinding(Exchange claimExchange, Queue flowClaimQueryQueue) {
        return BindingBuilder.bind(flowClaimQueryQueue).to(claimExchange).with(RoutingKeys.FLOW_CLAIM_QUERY).noargs();
    }

    @Bean
    public Binding loanUploadImageBinding(Exchange loanExchange, Queue loanUploadImageQueue) {
        return BindingBuilder.bind(loanUploadImageQueue).to(loanExchange).with(RoutingKeys.LOAN_UPLOAD_IMAGE).noargs();
    }

    @Bean
    public Binding loanUploadImageDelayBinding(Exchange loanExchange, Queue loanUploadImageDelayQueue) {
        return BindingBuilder.bind(loanUploadImageDelayQueue).to(loanExchange).with(RoutingKeys.LOAN_UPLOAD_IMAGE_DELAY).noargs();
    }

    @Bean
    public Binding repayDeductBinding(Exchange repayExchange, Queue repayDeductQueryQueue) {
        return BindingBuilder.bind(repayDeductQueryQueue).to(repayExchange).with(RoutingKeys.REPAY_DEDUCT_QUERY).noargs();
    }

    @Bean
    public Binding repayDeductDelayBinding(Exchange repayExchange, Queue repayDeductQueryDelayQueue) {
        return BindingBuilder.bind(repayDeductQueryDelayQueue).to(repayExchange).with(RoutingKeys.REPAY_DEDUCT_QUERY_DELAY).noargs();
    }

    @Bean
    public Binding repayApplyBinding(Exchange repayExchange, Queue repayApplyQueue) {
        return BindingBuilder.bind(repayApplyQueue).to(repayExchange).with(RoutingKeys.REPAY_APPLY).noargs();
    }

    @Bean
    public Binding gmxtTrustRepayApplyBinding(Exchange repayExchange, Queue gmxtTrustRepayApplyQueue) {
        return BindingBuilder.bind(gmxtTrustRepayApplyQueue).to(repayExchange).with(RoutingKeys.GMXT_TRUST_REPAY_APPLY).noargs();
    }

    @Bean
    public Binding gmxtTrustRepayQueryBinding(Exchange repayExchange, Queue gmxtTrustRepayQueryQueue) {
        return BindingBuilder.bind(gmxtTrustRepayQueryQueue).to(repayExchange).with(RoutingKeys.GMXT_TRUST_REPAY_QUERY).noargs();
    }


    @Bean
    public Binding gmxtTrustRepayQueryDelayBinding(Exchange repayExchange, Queue gmxtTrustRepayQueryDelayQueue) {
        return BindingBuilder.bind(gmxtTrustRepayQueryDelayQueue).to(repayExchange).with(RoutingKeys.GMXT_TRUST_REPAY_QUERY_DELAY).noargs();
    }


    @Bean
    public Binding batchRepayApplyBinding(Exchange repayExchange, Queue batchRepayApplyQueue) {
        return BindingBuilder.bind(batchRepayApplyQueue).to(repayExchange).with(RoutingKeys.BATCH_REPAY_APPLY).noargs();
    }

    @Bean
    public Binding batchRepayQueryBinding(Exchange repayExchange, Queue batchRepayQueryQueue) {
        return BindingBuilder.bind(batchRepayQueryQueue).to(repayExchange).with(RoutingKeys.BATCH_REPAY_QUERY).noargs();
    }


    @Bean
    public Binding batchRepayQueryDelayBinding(Exchange repayExchange, Queue batchRepayQueryDelayQueue) {
        return BindingBuilder.bind(batchRepayQueryDelayQueue).to(repayExchange).with(RoutingKeys.BATCH_REPAY_QUERY_DELAY).noargs();
    }


    @Bean
    public Binding repayBankNotifyBinding(Exchange repayExchange, Queue repayBankNotifyQueue) {
        return BindingBuilder.bind(repayBankNotifyQueue).to(repayExchange).with(RoutingKeys.REPAY_BANK_NOTIFY_RK).noargs();
    }

    @Bean
    public Binding repayBankBatchNotifyBinding(Exchange repayExchange, Queue repayBankBatchNotifyQueue) {
        return BindingBuilder.bind(repayBankBatchNotifyQueue).to(repayExchange).with(RoutingKeys.REPAY_BANK_BATCH_NOTIFY_RK).noargs();
    }

    @Bean
    public Binding substituteBankApplyBinding(Exchange repayExchange, Queue substituteBankApplyQueue) {
        return BindingBuilder.bind(substituteBankApplyQueue).to(repayExchange).with(RoutingKeys.SUBSTITUTE_BANK_APPLY_RK).noargs();
    }

    @Bean
    public Binding substituteBankApplyDelayBinding(Exchange repayExchange, Queue substituteBankApplyDelayQueue) {
        return BindingBuilder.bind(substituteBankApplyDelayQueue).to(repayExchange).with(RoutingKeys.SUBSTITUTE_BANK_APPLY_DELAY_RK).noargs();
    }

    @Bean
    public Binding repayRetryBinding(Exchange repayExchange, Queue repayCustomRetry) {
        return BindingBuilder.bind(repayCustomRetry).to(repayExchange).with(RoutingKeys.REPAY_RETRY_RK).noargs();
    }

    @Bean
    public Binding repaySubstituteApplyBinding(Exchange repayExchange, Queue repaySubstituteApply) {
        return BindingBuilder.bind(repaySubstituteApply).to(repayExchange).with(RoutingKeys.REPAY_SUBSTITUTE_APPLY).noargs();
    }

    @Bean
    public Binding repayBankNotifyDelayBinding(Exchange repayExchange, Queue repayBankNotifyDelayQueue) {
        return BindingBuilder.bind(repayBankNotifyDelayQueue).to(repayExchange).with(RoutingKeys.REPAY_BANK_NOTIFY_DELAY_RK).noargs();
    }

    /**
     * 代偿后通知binding
     *
     * @param repayExchange         还款exchange
     * @param claimAfterNotifyQueue 代偿后通知queue
     * @return 绑定
     */
    @Bean
    public Binding claimAfterNotifyQueueBinding(Exchange repayExchange, Queue claimAfterNotifyQueue) {
        return BindingBuilder.bind(claimAfterNotifyQueue).to(repayExchange).with(RoutingKeys.CLAIM_AFTER_NOTIFY).noargs();
    }

    @Bean
    public Binding claimAfterNotifyDelayQueueBinding(Exchange repayExchange, Queue claimAfterNotifyDelayQueue) {
        return BindingBuilder.bind(claimAfterNotifyDelayQueue).to(repayExchange).with(RoutingKeys.CLAIM_AFTER_NOTIFY_DELAY_RK).noargs();
    }

    @Bean
    public Binding signatureQueryQueueDelayBinding(Exchange signatureExchange, Queue signatureQueryDelayQueue) {
        return BindingBuilder.bind(signatureQueryDelayQueue).to(signatureExchange).with(RoutingKeys.SIGNATURE_QUERY_DELAY).noargs();
    }

    @Bean
    public Binding signatureQueryQueueBinding(Exchange signatureExchange, Queue signatureQueryQueue) {
        return BindingBuilder.bind(signatureQueryQueue).to(signatureExchange).with(RoutingKeys.SIGNATURE_QUERY).noargs();
    }


    @Bean
    public Binding signatureNewQueryDelayQueueBinding(Exchange signatureExchange, Queue signatureNewQueryDelayQueue) {
        return BindingBuilder.bind(signatureNewQueryDelayQueue).to(signatureExchange).with(RoutingKeys.SIGNATURE_NEW_QUERY_DELAY).noargs();
    }

    @Bean
    public Binding signatureNewQueryQueueBinding(Exchange signatureExchange, Queue signatureNewQueryQueue) {
        return BindingBuilder.bind(signatureNewQueryQueue).to(signatureExchange).with(RoutingKeys.SIGNATURE_NEW_QUERY).noargs();
    }

    @Bean
    public Binding signatureApplyQueueDelayBinding(Exchange signatureExchange, Queue signatureApplyDelayQueue) {
        return BindingBuilder.bind(signatureApplyDelayQueue).to(signatureExchange).with(RoutingKeys.SIGNATURE_APPLY_DELAY).noargs();
    }

    @Bean
    public Binding signatureApplyQueueBinding(Exchange signatureExchange, Queue signatureApplyQueue) {
        return BindingBuilder.bind(signatureApplyQueue).to(signatureExchange).with(RoutingKeys.SIGNATURE_APPLY).noargs();
    }



    @Bean
    public Binding contractDownloadQueueBinding(Exchange contractExchange, Queue contractDownloadQueue) {
        return BindingBuilder.bind(contractDownloadQueue).to(contractExchange).with(RoutingKeys.CONTRACT_DOWNLOAD).noargs();
    }

    @Bean
    public Binding contractDownloadQueueDelayBinding(Exchange contractExchange, Queue contractDownloadDelayQueue) {
        return BindingBuilder.bind(contractDownloadDelayQueue).to(contractExchange).with(RoutingKeys.CONTRACT_DOWNLOAD_DELAY).noargs();
    }

    @Bean
    public Binding gmxtContractDownloadQueueDelayBinding(Exchange contractExchange, Queue gmxtContractDownloadDelayQueue) {
        return BindingBuilder.bind(gmxtContractDownloadDelayQueue).to(contractExchange).with(RoutingKeys.GMXT_CONTRACT_DOWNLOAD_DELAY).noargs();
    }


    @Bean
    public Binding contractUploadQueueBinding(Exchange contractExchange, Queue contractUploadQueue) {
        return BindingBuilder.bind(contractUploadQueue).to(contractExchange).with(RoutingKeys.CONTRACT_UPLOAD).noargs();
    }

    @Bean
    public Binding contractUploadQueueDelayBinding(Exchange contractExchange, Queue contractUploadDelayQueue) {
        return BindingBuilder.bind(contractUploadDelayQueue).to(contractExchange).with(RoutingKeys.CONTRACT_UPLOAD_DELAY).noargs();
    }

    @Bean
    public Binding gmxtContractUploadQueueDelayBinding(Exchange contractExchange, Queue gmxtContractUploadDelayQueue) {
        return BindingBuilder.bind(gmxtContractUploadDelayQueue).to(contractExchange).with(RoutingKeys.GMXT_CONTRACT_UPLOAD_DELAY).noargs();
    }

    @Bean
    public Binding bankSignQueryQueueBinding(Exchange contractExchange, Queue bankSignQueryQueue) {
        return BindingBuilder.bind(bankSignQueryQueue).to(contractExchange).with(RoutingKeys.BANK_SIGN_QUERY).noargs();
    }

    @Bean
    public Binding bankSignQueryDelayQueueBinding(Exchange contractExchange, Queue bankSignQueryDelayQueue) {
        return BindingBuilder.bind(bankSignQueryDelayQueue).to(contractExchange).with(RoutingKeys.BANK_SIGN_QUERY_DELAY).noargs();
    }

    @Bean
    public Binding fileUploadNotifyDelayQueueBinding(Exchange loanExchange, Queue fileUploadNotifyDelayQueue) {
        return BindingBuilder.bind(fileUploadNotifyDelayQueue).to(loanExchange).with(RoutingKeys.FILE_UPLOAD_NOTIFY_DELAY).noargs();
    }

    @Bean
    public Binding fileUploadNotifyQueueBinding(Exchange loanExchange, Queue fileUploadNotifyQueue) {
        return BindingBuilder.bind(fileUploadNotifyQueue).to(loanExchange).with(RoutingKeys.FILE_UPLOAD_NOTIFY).noargs();
    }

    @Bean
    public Binding trustPlanChangeBindingDelay(Exchange trustExchange, Queue trustPlanChangeDelay) {
        return BindingBuilder.bind(trustPlanChangeDelay).to(trustExchange).with(RoutingKeys.TRUST_PLAN_CHANGE_DELAY).noargs();
    }

    @Bean
    public Binding trustPlanChangeBinding(Exchange trustExchange, Queue trustPlanChange) {
        return BindingBuilder.bind(trustPlanChange).to(trustExchange).with(RoutingKeys.TRUST_PLAN_CHANGE).noargs();
    }

    @Bean
    public Binding loanAfterAgreementSignBinding(Exchange loanExchange, Queue loanAfterAgreementSignQueue) {
        return BindingBuilder.bind(loanAfterAgreementSignQueue).to(loanExchange).with(RoutingKeys.LOAN_AFTER_AGREEMENT_SIGN).noargs();
    }

    @Bean
    public Binding loanAfterAgreementSignDelayQueueBinding(Exchange loanExchange, Queue loanAfterAgreementSignDelayQueue) {
        return BindingBuilder.bind(loanAfterAgreementSignDelayQueue).to(loanExchange).with(RoutingKeys.LOAN_AFTER_AGREEMENT_SIGN_DELAY).noargs();
    }

    @Bean
    public Binding covenantDownloadLoanContractQueueBinding(Exchange covenantExchange, Queue covenantDownloadLoanContractChange) {
        return BindingBuilder.bind(covenantDownloadLoanContractChange).to(covenantExchange).with(RoutingKeys.COVENANT_DOWNLOAD_LOAN_CONTRACT).noargs();
    }

    @Bean
    public Binding covenantDownloadLoanVoucherQueueBinding(Exchange covenantExchange, Queue covenantDownloadLoanVoucherChange) {
        return BindingBuilder.bind(covenantDownloadLoanVoucherChange).to(covenantExchange).with(RoutingKeys.COVENANT_DOWNLOAD_LOAN_VOUCHER).noargs();
    }

    @Bean
    public Binding covenantDownloadOtherQueueBinding(Exchange covenantExchange, Queue covenantDownloadOtherChange) {
        return BindingBuilder.bind(covenantDownloadOtherChange).to(covenantExchange).with(RoutingKeys.COVENANT_DOWNLOAD_OTHER).noargs();
    }

    @Bean
    public Binding covenantDownloadAgendaQueueBinding(Exchange covenantExchange, Queue covenantDownloadAgendaChange) {
        return BindingBuilder.bind(covenantDownloadAgendaChange).to(covenantExchange).with(RoutingKeys.COVENANT_DOWNLOAD_AGENDA).noargs();
    }

    @Bean
    public Binding covenantDownloadAsyncApplyQueueBinding(Exchange covenantExchange, Queue covenantDownloadAsyncApplyChange) {
        return BindingBuilder.bind(covenantDownloadAsyncApplyChange).to(covenantExchange).with(RoutingKeys.COVENANT_DOWNLOAD_ASYNC_APPLY).noargs();
    }

    @Bean
    public Binding covenantDownloadAsyncQueryQueueBinding(Exchange covenantExchange, Queue covenantDownloadAsyncQueryChange) {
        return BindingBuilder.bind(covenantDownloadAsyncQueryChange).to(covenantExchange).with(RoutingKeys.COVENANT_DOWNLOAD_ASYNC_QUERY).noargs();
    }

    @Bean
    public Binding covenantDownloadAsyncQueryDelayQueueBinding(Exchange covenantExchange, Queue covenantDownloadAsyncQueryDelayChange) {
        return BindingBuilder.bind(covenantDownloadAsyncQueryDelayChange).to(covenantExchange).with(RoutingKeys.COVENANT_DOWNLOAD_ASYNC_QUERY_DELAY).noargs();
    }


    @Bean
    public Binding defrayQueryBinding(Exchange trustExchange, Queue defrayQueryQueue) {
        return BindingBuilder.bind(defrayQueryQueue).to(trustExchange).with(RoutingKeys.DEFRAY_QUERY).noargs();
    }

    @Bean
    public Binding defrayQueryQueueDelayBinding(Exchange trustExchange, Queue defrayQueryQueueDelayQueue) {
        return BindingBuilder.bind(defrayQueryQueueDelayQueue).to(trustExchange).with(RoutingKeys.DEFRAY_QUERY_DELAY).noargs();
    }

    @Bean
    public Binding quotaQueryBinding(Exchange quotaExchange, Queue quotaQueryQueue) {
        return BindingBuilder.bind(quotaQueryQueue).to(quotaExchange).with(RoutingKeys.QUOTA_QUERY).noargs();
    }

    @Bean
    public Binding quotaQueryDelayBinding(Exchange quotaExchange, Queue quotaQueryDelayQueue) {
        return BindingBuilder.bind(quotaQueryDelayQueue).to(quotaExchange).with(RoutingKeys.QUOTA_QUERY_DELAY).noargs();
    }

    @Bean
    public Binding quotaAdjustApplyBinding(Exchange quotaExchange, Queue quotaAdjustApplyQueue) {
        return BindingBuilder.bind(quotaAdjustApplyQueue).to(quotaExchange).with(RoutingKeys.QUOTA_ADJUST_APPLY).noargs();
    }

    @Bean
    public Binding quotaAdjustApplyDelayBinding(Exchange quotaExchange, Queue quotaAdjustApplyDelayQueue) {
        return BindingBuilder.bind(quotaAdjustApplyDelayQueue).to(quotaExchange).with(RoutingKeys.QUOTA_ADJUST_APPLY_DELAY).noargs();
    }

    @Bean
    public Binding quotaAdjustResultQueryBinding(Exchange quotaExchange, Queue quotaAdjustResultQueryQueue) {
        return BindingBuilder.bind(quotaAdjustResultQueryQueue).to(quotaExchange).with(RoutingKeys.QUOTA_ADJUST_RESULT_QUERY).noargs();
    }

    @Bean
    public Binding quotaAdjustResultQueryDelayBinding(Exchange quotaExchange, Queue quotaAdjustResultQueryDelayQueue) {
        return BindingBuilder.bind(quotaAdjustResultQueryDelayQueue).to(quotaExchange).with(RoutingKeys.QUOTA_ADJUST_RESULT_QUERY_DELAY).noargs();
    }

    @Bean
    public Binding quotaAdjustResultCallbackBinding(Exchange quotaExchange, Queue quotaAdjustResultCallbackQueue) {
        return BindingBuilder.bind(quotaAdjustResultCallbackQueue).to(quotaExchange).with(RoutingKeys.QUOTA_ADJUST_RESULT_CALLBACK).noargs();
    }

    @Bean
    public Binding userInfoUpdateApplyBinding(Exchange creditExchange, Queue userInfoUpdateApplyQueue) {
        return BindingBuilder.bind(userInfoUpdateApplyQueue).to(creditExchange).with(RoutingKeys.USER_INFO_UPDATE_APPLY).noargs();
    }

    @Bean
    public Binding userInfoUpdateQueryBinding(Exchange creditExchange, Queue userInfoUpdateQueryQueue) {
        return BindingBuilder.bind(userInfoUpdateQueryQueue).to(creditExchange).with(RoutingKeys.USER_INFO_UPDATE_QUERY).noargs();
    }

    @Bean
    public Binding userInfoUpdateQueryDelayBinding(Exchange creditExchange, Queue userInfoUpdateQueryDelayQueue) {
        return BindingBuilder.bind(userInfoUpdateQueryDelayQueue).to(creditExchange).with(RoutingKeys.USER_INFO_UPDATE_QUERY_DELAY).noargs();
    }

    @Bean
    public Binding trustFileSignQueryQueueBinding(Exchange trustExchange, Queue trustFileSignQueryQueue) {
        return BindingBuilder.bind(trustFileSignQueryQueue).to(trustExchange).with(RoutingKeys.TRUST_FILE_SIGN_QUERY).noargs();
    }

    @Bean
    public Binding trustFileSignQueryQueueDelayBinding(Exchange trustExchange, Queue trustFileSignQueryQueueDelayQueue) {
        return BindingBuilder.bind(trustFileSignQueryQueueDelayQueue).to(trustExchange).with(RoutingKeys.TRUST_FILE_SIGN_QUERY_DELAY).noargs();
    }

    @Bean
    public Binding fpdSubstituteQueueBinding(Exchange repayExchange, Queue fpdSubstituteQueue) {
        return BindingBuilder.bind(fpdSubstituteQueue).to(repayExchange).with(RoutingKeys.FPD_SUBSTITUTE).noargs();
    }

    @Bean
    public Binding limiterRateUpdateQueueBinding(Exchange commonConfigExchange, Queue limiterRateUpdateQueue) {
        return BindingBuilder.bind(limiterRateUpdateQueue).to(commonConfigExchange).with(RoutingKeys.LIMITER_RATE_UPDATE).noargs();
    }

    @Bean
    public MqService mqService(RabbitTemplate rabbitTemplate) {
        return new MqService(rabbitTemplate);
    }

    public interface Exchanges {
        String CREDIT = "credit";
        String LOAN = "loan";
        String REPAY = "repay";
        String SIGNATURE = "signature";
        String TRUST = "trust";
        /**
         * 合同下载上传
         */
        String CONTRACT = "contract";
        /**
         * 放款后，协议文件上传下载
         */
        String COVENANT = "covenant";

        /**
         * 授信额度
         */
        String QUOTA = "quota";

        /**
         * 代偿
         */
        String CLAIM = "claim";

        /**
         * 通用的
         */
        String COMMON_CONFIG = "commonconfig";

        void exchange();
    }

    public interface Queues {
        String CREDIT_QUERY_DELAY = "credit.result.query.delay";
        String CREDIT_QUERY = "credit.result.query";

        String CREDIT_ADJUST_QUERY_DELAY = "credit.adjust.query.delay";
        String CREDIT_ADJUST_QUERY = "credit.adjust.query";

        /**
         * 调额新延迟队列，和老的富民区分
         */
        String CREDIT_NEW_ADJUST_QUERY_DELAY = "credit.new.adjust.query.delay";
        /**
         * 调额新队列，和老的富民区分
         */
        String CREDIT_NEW_ADJUST_QUERY = "credit.new.adjust.query";


        String ZYEBANK_BIND_CHANGE_DELAY = "zyebank_bind_change_delay";
        String ZYEBANK_BIND_CHANGE = "zyebank_bind_change";

        String CREDIT_STAGE_UPLOAD_DELAY = "credit.stage.upload.delay";
        String CREDIT_STAGE_UPLOAD = "credit.stage.upload";

        /**
         * 授信阶段协议重生成并签章
         */
        String CREDIT_AGREEMENT_RECREATE = "credit.agreement.recreate";

        /**
         * 放款后协议签署
         */
        String LOAN_AFTER_AGREEMENT_SIGN = "loan.after.agreement.sign";
        String LOAN_AFTER_AGREEMENT_SIGN_DELAY = "loan.after.agreement.sign.delay";


        /**
         * 风控失败授信推送资方
         */
        String CREDIT_FAILED_NOTIFY_DELAY = "credit.failed.notify.delay";
        String CREDIT_FAILED_NOTIFY = "credit.failed.notify";


        String LOAN_QUERY_DELAY = "loan.result.query.delay";
        String LOAN_QUERY = "loan.result.query";

        String LOAN_APPLY_DELAY = "loan.apply.delay";
        String LOAN_APPLY = "loan.apply";

        String LOAN_REPLAN_QUERY_DELAY = "loan.replan.result.query.delay";

        String LOAN_REPLAN_QUERY = "loan.replan.result.query";


        String LOAN_REPLAN_SYNC_DELAY = "loan.replan.sync.delay";

        String LOAN_REPLAN_SYNC = "loan.replan.sync";

        String LOAN_REPLAN_SYNC_MANUAL = "loan.replan.sync.manual";


        String QINJIA_FILE_UPLOAD_DELAY = "qinjia.file.upload.delay";
        String QINJIA_FILE_UPLOAD = "qinjia.file.upload";

        String REPAY_QUERY_DELAY = "repay.result.query.delay";
        String REPAY_QUERY = "repay.result.query";

        String CLAIM_APPLY_DELAY = "claim.apply.delay";
        String CLAIM_APPLY = "claim.apply";

        String CLAIM_QUERY_DELAY = "claim.query.delay";
        String CLAIM_QUERY = "claim.query";

        /**
         * 流量方代偿申请通知
         */
        String FLOW_CLAIM_APPLY_DELAY = "flow.claim.apply.delay";
        String FLOW_CLAIM_APPLY = "flow.claim.apply";

        /**
         * 流量方代偿查询通知
         */
        String FLOW_CLAIM_QUERY_DELAY = "flow.claim.query.delay";
        String FLOW_CLAIM_QUERY = "flow.claim.query";

        String LOAN_UPLOAD_IMAGE = "loan.upload.image";
        String LOAN_UPLOAD_IMAGE_DELAY = "loan.upload.image.delay";

        String REPAY_DEDUCT_QUERY = "repay.deduct.query";
        String REPAY_DEDUCT_QUERY_DELAY = "repay.deduct.query.delay";

        String REPAY_APPLY = "repay.apply";
        String BATCH_REPAY_APPLY = "batch.repay.apply";

        String BATCH_REPAY_QUERY = "batch.repay.query";

        String BATCH_REPAY_QUERY_DELAY = "batch.repay.query.delay";

        String GMXT_TRUST_REPAY_APPLY = "gmxt.trust.repay.apply";

        String GMXT_TRUST_REPAY_QUERY = "gmxt.trust.repay.query";

        String GMXT_TRUST_REPAY_QUERY_DELAY = "gmxt.trust.repay.query.delay";

        String REPAY_BANK_NOTIFY_QUEUE = "repay.bank.notify";

        String REPAY_BANK_NOTIFY_DELAY_QUEUE = "repay.bank.notify.delay";

        String SUBSTITUTE_BANK_APPLY_QUEUE = "substitute.bank.apply";

        String SUBSTITUTE_BANK_APPLY_DELAY_QUEUE = "substitute.bank.apply.delay";

        String REPAY_BANK_BATCH_NOTIFY_QUEUE = "repay.bank.batch.notify";


        String REPAY_RETRY_QUEUE = "repay.custom.retry";

        /**
         * 代还申请
         */
        String REPAY_SUBSTITUTE_APPLY = "repay.substitute.apply";

        /**
         * 代偿后还款通知
         */
        String CLAIM_AFTER_NOTIFY = "claim.after.notify";

        /**
         * 代偿后还款通知
         */
        String CLAIM_AFTER_NOTIFY_DELAY = "claim.after.notify.delay";



        /**
         * 签章自定义重签队列
         */
        String SIGNATURE_CUSTOM_AGAIN_APPLY = "signature.custom.again.apply";

        String SIGNATURE_APPLY_DELAY = "signature.apply.delay";

        String SIGNATURE_APPLY = "signature.apply";

        String SIGNATURE_QUERY_DELAY = "signature.query.delay";

        String SIGNATURE_QUERY = "signature.query";

        String SIGNATURE_NEW_QUERY_DELAY = "signature.new.query.delay";

        String SIGNATURE_NEW_QUERY = "signature.new.query";


        String CONTRACT_DOWNLOAD = "contract.download";

        String CONTRACT_DOWNLOAD_DELAY = "contract.download.delay";

        String CONTRACT_UPLOAD = "contract.upload";

        String CONTRACT_UPLOAD_DELAY = "contract.upload.delay";

        /**
         * 资方电子签章查询
         */
        String BANK_SIGN_QUERY = "bank.sign.query";

        String BANK_SIGN_QUERY_DELAY = "bank.sign.query.delay";

        String FILE_UPLOAD_NOTIFY = "file.upload.notify";

        String FILE_UPLOAD_NOTIFY_DELAY = "file.upload.notify.delay";

        String GMXT_USING_LETTERS_QUERY = "gmxt.using.letters.query";
        String GMXT_USING_LETTERS_QUERY_DELAY = "gmxt.using.letters.query.delay";

        String GMXT_CONTRACT_UPLOAD_DELAY = "gmxt.contract.upload.delay";

        String GMXT_CREDIT_QUERY_DELAY = "gmxt.credit.query.delay";
        String GMXT_CONTRACT_DOWNLOAD_DELAY = "gmxt.contract.download.delay";
        String GMXT_LOAN_QUERY_DELAY = "gmxt.loan.query.delay";
        String TRUST_PLAN_CHANGE = "trust.plan.change";
        String TRUST_PLAN_CHANGE_DELAY = "trust.plan.change.delay";

        String DEFRAY_QUERY = "defray.query";
        String DEFRAY_QUERY_DELAY = "defray.query.delay";

        /**
         * 放款后，协议文件下载
         */
        String COVENANT_DOWNLOAD_LOAN_CONTRACT = "covenant.download.loan.contract";
        String COVENANT_DOWNLOAD_LOAN_VOUCHER = "covenant.download.loan.voucher";
        String COVENANT_DOWNLOAD_OTHER = "covenant.download.other";
        String COVENANT_DOWNLOAD_AGENDA = "covenant.download.agenda";
        String COVENANT_DOWNLOAD_ASYNC_APPLY = "covenant.download.async.apply";
        String COVENANT_DOWNLOAD_ASYNC_QUERY = "covenant.download.async.query";

        String COVENANT_DOWNLOAD_ASYNC_QUERY_DELAY = "covenant.download.async.query.delay";

        /**
         * 额度查询
         */
        String QUOTA_QUERY = "quota.query";
        /**
         * 额度查询延迟
         */
        String QUOTA_QUERY_DELAY = "quota.query.delay";
        /**
         * 调额申请
         */
        String QUOTA_ADJUST_APPLY = "quota.adjust.apply";
        /**
         * 调额申请延迟
         */
        String QUOTA_ADJUST_APPLY_DELAY = "quota.adjust.apply.delay";
        /**
         * 调额结果查询
         */
        String QUOTA_ADJUST_RESULT_QUERY = "quota.adjust.result.query";
        /**
         * 调额结果查询延迟
         */
        String QUOTA_ADJUST_RESULT_QUERY_DELAY = "quota.adjust.result.query.delay";
        /**
         * 调额结果回调
         */
        String QUOTA_ADJUST_RESULT_CALLBACK = "quota.adjust.result.callback";

        /**
         * 用户信息更新申请
         */
        String USER_INFO_UPDATE_APPLY = "user.info.update.apply";
        /**
         * 用户信息更新结果查询
         */
        String USER_INFO_UPDATE_QUERY = "user.info.update.query";
        /**
         * 用户信息更新结果查询延迟
         */
        String USER_INFO_UPDATE_QUERY_DELAY = "user.info.update.query.delay";

        /**
         * 延迟查询信托签章文件
         */
        String TRUST_FILE_SIGN_QUERY = "trust.file.sign.query";
        String TRUST_FILE_SIGN_QUERY_DELAY = "trust.file.sign.query.delay";

        /**
         * fpd数据处理代还
         */
        String FPD_SUBSTITUTE = "fpd.substitute";

        /**
         * 限流器限制频率更新
         */
        String LIMITER_RATE_UPDATE = "limiter.rate.update";

        void queue();
    }

    public interface RoutingKeys {
        String CREDIT_QUERY_DELAY = "credit.query.delay";
        String CREDIT_QUERY = "credit.query";

        String ZYEBANK_BIND_CHANGE_DELAY = "zyebank_bind_change_delay";
        String ZYEBANK_BIND_CHANGE = "zyebank_bind_change";

        String CREDIT_STAGE_UPLOAD_DELAY = "credit.stage.upload.delay";
        String CREDIT_STAGE_UPLOAD = "credit.stage.upload";


        String LOAN_AFTER_AGREEMENT_SIGN = "loan.after.agreement.sign";

        String LOAN_AFTER_AGREEMENT_SIGN_DELAY = "loan.after.agreement.sign.delay";

        /**
         * 风控失败授信推送资方
         */
        String CREDIT_FAILED_NOTIFY_DELAY = "credit.failed.notify.delay";
        String CREDIT_FAILED_NOTIFY = "credit.failed.notify";

        String LOAN_QUERY_DELAY = "loan.query.delay";
        String LOAN_QUERY = "loan.query";

        String LOAN_APPLY_DELAY = "loan.apply.delay";
        String LOAN_APPLY = "loan.apply";

        String LOAN_REPLAN_QUERY_DELAY = "loan.replan.query.delay";
        String LOAN_REPLAN_QUERY = "loan.replan.query";

        String LOAN_REPLAN_SYNC_DELAY = "loan.replan.sync.delay";

        String LOAN_REPLAN_SYNC = "loan.replan.sync";

        /**
         * 还款计划同步字符手动处理
         */
        String LOAN_REPLAN_SYNC_MANUAL = "loan.replan.sync.manual";


        String QINJIA_FILE_UPLOAD_DELAY = "qinjia.file.upload.delay";

        String QINJIA_FILE_UPLOAD = "qinjia.file.upload";

        String REPAY_QUERY_DELAY = "repay.query.delay";
        String REPAY_QUERY = "repay.query";

        String CLAIM_APPLY_DELAY = "claim.apply.delay";
        String CLAIM_APPLY = "claim.apply";

        String CLAIM_QUERY_DELAY = "claim.query.delay";
        String CLAIM_QUERY = "claim.query";

        /**
         * 流量方代偿申请通知
         */
        String FLOW_CLAIM_APPLY_DELAY = "flow.claim.apply.delay";
        String FLOW_CLAIM_APPLY = "flow.claim.apply";

        /**
         * 流量方代偿查询通知
         */
        String FLOW_CLAIM_QUERY_DELAY = "flow.claim.query.delay";
        String FLOW_CLAIM_QUERY = "flow.claim.query";

        /**
         * 代偿后还款通知
         */
        String CLAIM_AFTER_NOTIFY = "claim.after.notify";

        String CLAIM_AFTER_NOTIFY_DELAY_RK = "claim.after.notify.delay";

        String SIGNATURE_APPLY_DELAY = "signature.apply.delay";

        String SIGNATURE_APPLY = "signature.apply";

        String SIGNATURE_QUERY_DELAY = "signature.query.delay";

        String SIGNATURE_QUERY = "signature.query";

        String SIGNATURE_NEW_QUERY_DELAY = "signature.new.query.delay";

        String SIGNATURE_NEW_QUERY = "signature.new.query";


        String CREDIT_ADJUST_QUERY_DELAY = "credit.adjust.query.delay";

        String CREDIT_ADJUST_QUERY = "credit.adjust.query";


        String CREDIT_NEW_ADJUST_QUERY_DELAY = "credit.new.adjust.query.delay";

        String CREDIT_NEW_ADJUST_QUERY = "credit.new.adjust.query";


        String LOAN_UPLOAD_IMAGE = "loan.upload.image";
        String LOAN_UPLOAD_IMAGE_DELAY = "loan.upload.image.delay";

        String REPAY_DEDUCT_QUERY = "repay.deduct.query";
        String REPAY_DEDUCT_QUERY_DELAY = "repay.deduct.query.delay";

        String REPAY_APPLY = "repay.apply";
        String BATCH_REPAY_APPLY = "batch.repay.apply";
        String BATCH_REPAY_QUERY = "batch.repay.query";
        String BATCH_REPAY_QUERY_DELAY = "batch.repay.query.delay";

        String GMXT_TRUST_REPAY_APPLY = "gmxt.trust.repay.apply";

        String GMXT_TRUST_REPAY_QUERY = "gmxt.trust.repay.query";

        String GMXT_TRUST_REPAY_QUERY_DELAY = "gmxt.trust.repay.query.delay";

        String REPAY_BANK_NOTIFY_RK = "repay.bank.notify";

        String REPAY_BANK_NOTIFY_DELAY_RK = "repay.bank.notify.delay";

        String SUBSTITUTE_BANK_APPLY_RK = "substitute.bank.apply";

        String SUBSTITUTE_BANK_APPLY_DELAY_RK = "substitute.bank.apply.delay";

        String REPAY_BANK_BATCH_NOTIFY_RK = "repay.bank.batch.notify";

        String REPAY_RETRY_RK = "repay.custom.retry";

        String REPAY_SUBSTITUTE_APPLY = "repay.substitute.apply";

        String CONTRACT_DOWNLOAD = "contract.download";
        String CONTRACT_DOWNLOAD_DELAY = "contract.download.delay";

        String CONTRACT_UPLOAD = "contract.upload";
        String CONTRACT_UPLOAD_DELAY = "contract.upload.delay";

        /**
         * 资方电子签章查询
         */
        String BANK_SIGN_QUERY = "bank.sign.query";

        String BANK_SIGN_QUERY_DELAY = "bank.sign.query.delay";

        String GMXT_CONTRACT_UPLOAD_DELAY = "gmxt.contract.upload.delay";

        String GMXT_CREDIT_QUERY_DELAY = "gmxt.credit.query.delay";

        String GMXT_CONTRACT_DOWNLOAD_DELAY = "gmxt.contract.download.delay";

        String GMXT_LOAN_QUERY_DELAY = "gmxt.loan.query.delay";

        String FILE_UPLOAD_NOTIFY = "file.upload.notify";

        String FILE_UPLOAD_NOTIFY_DELAY = "file.upload.notify.delay";


        String GMXT_USING_LETTERS_QUERY = "gmxt.using.letters.query";

        String GMXT_USING_LETTERS_QUERY_DELAY = "gmxt.using.letters.query.delay";

        String TRUST_PLAN_CHANGE = "trust.plan.change";
        String TRUST_PLAN_CHANGE_DELAY = "trust.plan.change.delay";

        String DEFRAY_QUERY = "defray.query";
        String DEFRAY_QUERY_DELAY = "defray.query.delay";

        /**
         * 放款后，借款合同下载
         */
        String COVENANT_DOWNLOAD_LOAN_CONTRACT = "covenant.download.loan.contract";

        /**
         * 放款凭证
         */
        String COVENANT_DOWNLOAD_LOAN_VOUCHER = "covenant.download.loan.voucher";

        /**
         * 除借款合同、放款凭证外所有合同
         */
        String COVENANT_DOWNLOAD_OTHER = "covenant.download.other";

        /**
         * （特殊资方）文件以日期为单位的整体下载
         */
        String COVENANT_DOWNLOAD_AGENDA = "covenant.download.agenda";

        /**
         * 需要申请下载的合同申请
         */
        String COVENANT_DOWNLOAD_ASYNC_APPLY = "covenant.download.async.apply";

        /**
         * 需要申请下载的合同查询
         */
        String COVENANT_DOWNLOAD_ASYNC_QUERY = "covenant.download.async.query";

        /**
         * 需要申请下载的合同延迟查询
         */
        String COVENANT_DOWNLOAD_ASYNC_QUERY_DELAY = "covenant.download.async.query.delay";

        /**
         * 额度查询
         */
        String QUOTA_QUERY = "quota.query";
        /**
         * 额度查询延迟
         */
        String QUOTA_QUERY_DELAY = "quota.query.delay";
        /**
         * 调额申请
         */
        String QUOTA_ADJUST_APPLY = "quota.adjust.apply";
        /**
         * 调额申请延迟
         */
        String QUOTA_ADJUST_APPLY_DELAY = "quota.adjust.apply.delay";
        /**
         * 调额结果查询
         */
        String QUOTA_ADJUST_RESULT_QUERY = "quota.adjust.result.query";
        /**
         * 调额结果查询延迟
         */
        String QUOTA_ADJUST_RESULT_QUERY_DELAY = "quota.adjust.result.query.delay";
        /**
         * 调额结果回调
         */
        String QUOTA_ADJUST_RESULT_CALLBACK = "quota.adjust.result.callback";

        /**
         * 用户信息更新申请
         */
        String USER_INFO_UPDATE_APPLY = "user.info.update.apply";
        /**
         * 用户信息更新结果查询
         */
        String USER_INFO_UPDATE_QUERY = "user.info.update.query";
        /**
         * 用户信息更新结果查询延迟
         */
        String USER_INFO_UPDATE_QUERY_DELAY = "user.info.update.query.delay";

        /**
         * 延迟查询信托签章文件
         */
        String TRUST_FILE_SIGN_QUERY = "trust.file.sign.query";
        String TRUST_FILE_SIGN_QUERY_DELAY = "trust.file.sign.query.delay";

        /**
         * fpd数据处理代还
         */
        String FPD_SUBSTITUTE = "fpd.substitute";

        /**
         * 限流器限制频率更新
         */
        String LIMITER_RATE_UPDATE = "limiter.rate.update";

        void routing();
    }
}
