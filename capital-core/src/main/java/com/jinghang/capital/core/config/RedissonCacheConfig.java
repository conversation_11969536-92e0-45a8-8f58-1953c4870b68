package com.jinghang.capital.core.config;


import org.redisson.api.RedissonClient;
import org.redisson.spring.cache.CacheConfig;
import org.redisson.spring.cache.RedissonSpringCacheManager;
import org.springframework.cache.CacheManager;
import org.springframework.cache.annotation.EnableCaching;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

@Configuration
@EnableCaching
public class RedissonCacheConfig {

    public static final int CACHE_TTL = 5 * 60 * 1000;
    public static final int MAX_IDLE_TIME = 3 * 60 * 1000;

    @Bean
    CacheManager cacheManager(RedissonClient redissonClient) {
        Map<String, CacheConfig> config = new ConcurrentHashMap<String, CacheConfig>();

        // create "trialCacheMap" cache with ttl = 5 minutes and maxIdleTime = 3 minutes
        config.put("trialCapitalCacheMap", new CacheConfig(CACHE_TTL, MAX_IDLE_TIME));
        config.put("trialCacheMap", new CacheConfig(CACHE_TTL, MAX_IDLE_TIME));
        return new RedissonSpringCacheManager(redissonClient, config);
    }
}
