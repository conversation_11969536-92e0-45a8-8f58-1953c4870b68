package com.jinghang.capital.core.controller;

import com.jinghang.capital.api.ReccService;
import com.jinghang.capital.api.dto.RestResult;
import com.jinghang.capital.api.dto.recc.*;
import com.jinghang.capital.core.convert.apivo.ReccConvert;
import com.jinghang.capital.core.entity.CYBKReconcileFile;
import com.jinghang.capital.core.service.ManageService;
import com.jinghang.capital.core.vo.recc.ReccApplyVo;
import com.jinghang.capital.core.vo.recc.ReccDownloadVo;
import com.jinghang.capital.core.vo.recc.ReccFileApplyVo;
import com.jinghang.capital.core.vo.recc.ReccResultVo;
import com.jinghang.common.util.CollectionUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;
import java.util.stream.Collectors;

/**
 * 对账
 */
@RestController
@RequestMapping("recc")
public class ReccController implements ReccService {

    private static final Logger logger = LoggerFactory.getLogger(ReccController.class);
    private final ManageService manageService;

    @Autowired
    public ReccController(ManageService manageService) {
        this.manageService = manageService;
    }

    @Override
    public RestResult<ReccResultDto> process(ReccApplyDto reccApply) {
        logger.info("处理对账文件, channel: {}, reccDay: {}, reccType: {}", reccApply.getChannel(),
            reccApply.getReccDay(), reccApply.getReccType());
        ReccApplyVo applyVo = ReccConvert.INSTANCE.toApplyVo(reccApply);
        ReccResultVo reccResultVo = manageService.processRecc(applyVo);
        ReccResultDto resultDto = ReccConvert.INSTANCE.toResultDto(reccResultVo);
        return RestResult.success(resultDto);
    }

    @Override
    public RestResult<ReccResultDto> query(ReccApplyDto reccApply) {
        logger.info("query recc task, channel: {}, reccDay: {}, reccType: {}", reccApply.getChannel(),
            reccApply.getReccDay(), reccApply.getReccType());
        ReccApplyVo applyVo = ReccConvert.INSTANCE.toApplyVo(reccApply);
        ReccResultVo reccResultVo = manageService.query(applyVo);
        ReccResultDto resultDto = ReccConvert.INSTANCE.toResultDto(reccResultVo);
        return RestResult.success(resultDto);
    }

    @Override
    public RestResult<ReccResultDto> download(ReccDownloadDto dto) {
        logger.info("对账文件下载, channel: {}, pullDate: {}, reccType: {}", dto.getChannel(),
            dto.getPullDate(), dto.getReccType());
        ReccDownloadVo vo = ReccConvert.INSTANCE.toDownloadVo(dto);
        ReccResultVo reccResultVo = manageService.download(vo);
        ReccResultDto resultDto = ReccConvert.INSTANCE.toResultDto(reccResultVo);
        return RestResult.success(resultDto);
    }

    @Override
    public RestResult<ReccFileListDto> fileApply(ReccFileApplyDto dto) {
        logger.info("对账文件申请, channel: {}, fileDate: {}, company: {}", dto.getChannel(),
                dto.getFileDate(), dto.getCompany().name());
        ReccFileApplyVo vo = ReccConvert.INSTANCE.toFileApplyVo(dto);
        List<CYBKReconcileFile> files = manageService.fileApply(vo);
        ReccFileListDto reccFileListDto = new ReccFileListDto();
        if (CollectionUtil.isNotEmpty(files)) {
            reccFileListDto.setReccFiles(files.stream().map(file -> ReccConvert.INSTANCE.toReccFileDto(file)).collect(Collectors.toList()));
        }
        return RestResult.success(reccFileListDto);
    }


}
