package com.jinghang.capital.core.controller;

import com.jinghang.capital.api.DBTestService;
import com.jinghang.capital.api.dto.*;
import com.jinghang.capital.api.dto.repay.PartRepayType;
import com.jinghang.capital.api.dto.repay.RepayCategory;
import com.jinghang.capital.api.dto.test.DBCrudDto;
import com.jinghang.capital.core.entity.*;
import com.jinghang.capital.core.enums.*;
import com.jinghang.capital.core.enums.BankChannel;
import com.jinghang.capital.core.enums.FileType;
import com.jinghang.capital.core.enums.FlowChannel;
import com.jinghang.capital.core.enums.ProcessStatus;
import com.jinghang.capital.core.enums.RepayStatus;
import com.jinghang.capital.core.repository.*;
import com.netflix.discovery.converters.Auto;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @date 2025-08-21 16:59
 */
@RestController
@RequestMapping("test")
public class TestController implements DBTestService {

    @Autowired
    private CreditRepository creditRepository;
    @Autowired
    private CYBKCreditFlowRepository cybkCreditFlowRepository;
    @Autowired
    private AgreementSignatureRepository agreementSignatureRepository;
    @Autowired
    private LoanFileRepository loanFileRepository;
    @Autowired
    private LoanRepository loanRepository;
    @Autowired
    private LoanReplanRepository loanReplanRepository;
    @Autowired
    private CustomerRepayRecordRepository customerRepayRecordRepository;
    @Autowired
    private BankRepayRecordRepository bankRepayRecordRepository;

    @Override
    public RestResult<String> test(DBCrudDto dto) {
        RestResult<String> result = null;
        // 测试CRUD,包括 授信、放款、还款流程中的表
        String testId = "TEST0000001";
        try {
            // 1.授信
            // credit
            // ===== 插入一条数据 =====
            Credit credit = new Credit();
            credit.setId(testId);
            credit.setFlowChannel(FlowChannel.LVXIN);
            credit.setAccountId("123123");
            credit.setCreditAmt(new BigDecimal("20000.00"));
            credit.setPeriods(12);
            credit.setApplyTime(LocalDateTime.now());
            credit.setChannel(BankChannel.CYBK);
            credit.setCreditStatus(CreditStatus.FAIL);
            credit.setBankRate(new BigDecimal("0.08"));
            credit.setCustomRate(new BigDecimal("0.08"));
            credit.setCreatedBy("system");
            credit.setCreatedTime(LocalDateTime.now());
            credit.setUpdatedBy("system");
            credit.setUpdatedTime(LocalDateTime.now());

            creditRepository.save(credit);
            System.out.println("插入 Credit 成功, ID=" + credit.getId());

            // ===== 更新某个字段 =====
            credit.setCreditStatus(CreditStatus.FAIL);
            credit.setUpdatedBy("admin");
            credit.setUpdatedTime(LocalDateTime.now());
            creditRepository.save(credit);
            System.out.println("更新 Credit 成功, ID=" + credit.getId());

            // ===== 查询 =====
            creditRepository.findById(credit.getId())
                    .ifPresent(c -> System.out.println("查询 Credit 成功, state=" + c.getCreditStatus()));

            // ===== 删除 =====
            creditRepository.deleteById(credit.getId());
            System.out.println("删除 Credit 成功, ID=" + credit.getId());

            // CYBKCreditFlow
            // ====== CYBKCreditFlow ======
            CYBKCreditFlow cybkCreditFlow = new CYBKCreditFlow();
            cybkCreditFlow.setId(testId);
            cybkCreditFlow.setCreditId("credit_" + testId);
            cybkCreditFlow.setCustId("cust_" + testId);
            cybkCreditFlow.setCreditNo("cy_credit_" + testId);
            cybkCreditFlow.setLoanNo("cy_loan_" + testId);
            cybkCreditFlow.setLoanSeq("cy_loan_seq_" + testId);
            cybkCreditFlow.setRepaymentCode("repay_code_" + testId);
            cybkCreditFlow.setFundingModel(FundingModel.ALONE);
            cybkCreditFlow.setCreatedBy("system");
            cybkCreditFlow.setCreatedTime(LocalDateTime.now());
            cybkCreditFlowRepository.save(cybkCreditFlow);

            cybkCreditFlow.setFundingModel(FundingModel.CONFLATE);
            cybkCreditFlow.setUpdatedBy("admin");
            cybkCreditFlow.setUpdatedTime(LocalDateTime.now());
            cybkCreditFlowRepository.save(cybkCreditFlow);

            cybkCreditFlowRepository.findById(cybkCreditFlow.getId())
                    .ifPresent(flow -> System.out.println("CYBKCreditFlow 查询成功: " + flow.getFundingModel()));
            cybkCreditFlowRepository.deleteById(cybkCreditFlow.getId());


            // AgreementSignature
            // ====== AgreementSignature ======
            AgreementSignature agreementSignature = new AgreementSignature();
            agreementSignature.setId(testId);
            agreementSignature.setBusinessId("business_" + testId);
            agreementSignature.setChannel(BankChannel.CYBK);
            agreementSignature.setFileType(FileType.LOAN_CONTRACT);
            agreementSignature.setLoanStage(LoanStage.CREDIT);
            agreementSignature.setTemplateNo("TEMP_001");
            agreementSignature.setAddress("北京市朝阳区");
            agreementSignature.setBankMobilePhone("***********");
            agreementSignature.setIdentNo("110101199001011234");
            agreementSignature.setPersonName("测试用户");
            agreementSignature.setDynamicOssBucket("test-bucket");
            agreementSignature.setDynamicOssKey("test-key");
            agreementSignature.setSignState(ProcessStatus.SUCCESS);
            agreementSignature.setSignatureType(SignatureType.TEMPLATE);
            agreementSignature.setCreatedBy("system");
            agreementSignature.setCreatedTime(LocalDateTime.now());
            agreementSignatureRepository.save(agreementSignature);

            agreementSignature.setSignState(ProcessStatus.SUCCESS);
            agreementSignature.setCommonUserId("user_123");
            agreementSignature.setCommonTaskId("task_456");
            agreementSignature.setOssFileKey("contracts/signed_contract.pdf");
            agreementSignature.setOssFileUrl("https://oss.example.com/contracts/signed_contract.pdf");
            agreementSignature.setContractNo("CONTRACT_20250822");
            agreementSignature.setUpdatedBy("admin");
            agreementSignature.setUpdatedTime(LocalDateTime.now());
            agreementSignatureRepository.save(agreementSignature);

            agreementSignatureRepository.findById(agreementSignature.getId())
                    .ifPresent(as -> System.out.println("AgreementSignature 查询成功: " + as.getSignState()));
            agreementSignatureRepository.deleteById(agreementSignature.getId());

            // LoanFile
            // ====== LoanFile ======
            LoanFile loanFile = new LoanFile();
            loanFile.setId(testId);
            loanFile.setCreditId("credit_" + testId);
            loanFile.setRelatedId("related_" + testId);
            loanFile.setChannel(BankChannel.CYBK);
            loanFile.setStage("LOAN");
            loanFile.setFileType(FileType.LOAN_CONTRACT);
            loanFile.setFileName("loan_contract_20250822.pdf");
            loanFile.setSignStatus(ProcessStatus.SUCCESS);
            loanFile.setOssUnsignKey("unsigned/loan_contract.pdf");
            loanFile.setOssUnsignBucket("loan-documents");
            loanFile.setOssKey("signed/loan_contract.pdf");
            loanFile.setOssBucket("loan-documents");
            loanFile.setCreatedBy("system");
            loanFile.setCreatedTime(LocalDateTime.now());
            loanFileRepository.save(loanFile);

            loanFile.setSignStatus(ProcessStatus.SUCCESS);
            loanFile.setOssKey("signed/loan_contract_final.pdf");
            loanFile.setUpdatedBy("admin");
            loanFile.setUpdatedTime(LocalDateTime.now());
            loanFileRepository.save(loanFile);

            loanFileRepository.findById(loanFile.getId())
                    .ifPresent(lf -> System.out.println("LoanFile 查询成功: " + lf.getSignStatus()));
            loanFileRepository.deleteById(loanFile.getId());

            // 2.放款
            // Loan
            // ====== Loan ======
            Loan loan = new Loan();
            loan.setId(testId);
            loan.setAccountId("account_" + testId);
            loan.setCreditId("credit_" + testId);
            loan.setOuterLoanId("outer_loan_" + testId);
            loan.setChannel(BankChannel.CYBK);
            loan.setGuaranteeCompany(GuaranteeCompany.CJRD);
            loan.setProductNo("PROD_20250822");
            loan.setFlowChannel(FlowChannel.LVXIN);
            loan.setLoanAmt(new BigDecimal("50000.00"));
            loan.setPeriods(12);
            loan.setLoanPurpose(LoanPurpose.SHOPPING);
            loan.setLoanStatus(LoanStatus.SUCCESS);
            loan.setAssistMode("NORMAL");
            loan.setLoanNo("LOAN_NO_" + testId);
            loan.setLoanApplyContractNo("APPLY_CONTRACT_" + testId);
            loan.setGuaranteeContractNo("GUARANTEE_CONTRACT_" + testId);
            loan.setLoanContractNo("LOAN_CONTRACT_" + testId);
            loan.setLoanChannel("CYBK");
            loan.setLoanCardId("loan_card_" + testId);
            loan.setRepayCardId("repay_card_" + testId);
            loan.setCustName("测试用户");
            loan.setCustMobile("***********");
            loan.setCustCertNo("110101199001011234");
            loan.setCustomRate(new BigDecimal("0.12"));
            loan.setBankRate(new BigDecimal("0.08"));
            loan.setChannelType(BankChannelType.TYPE_V1);
            loan.setFundingModel(FundingModel.ALONE);
            loan.setCreatedBy("system");
            loan.setCreatedTime(LocalDateTime.now());
            loanRepository.save(loan);

            loan.setLoanStatus(LoanStatus.SUCCESS);
            loan.setLoanTime(LocalDateTime.now());
            loan.setUpdatedBy("admin");
            loan.setUpdatedTime(LocalDateTime.now());
            loanRepository.save(loan);

            loanRepository.findById(loan.getId())
                    .ifPresent(ln -> System.out.println("Loan 查询成功: " + ln.getLoanStatus()));
            loanRepository.deleteById(loan.getId());

            // ====== LoanReplan ======
            LoanReplan loanReplan = new LoanReplan();
            loanReplan.setId(testId);
            loanReplan.setLoanId("loan_" + testId);
            loanReplan.setChannel("CYBK");
            loanReplan.setPeriod(1);
            loanReplan.setRepayDate(LocalDate.now().plusMonths(1));
            loanReplan.setTotalAmt(new BigDecimal("4583.33"));
            loanReplan.setPrincipalAmt(new BigDecimal("4166.67"));
            loanReplan.setInterestAmt(new BigDecimal("416.66"));
            loanReplan.setPenaltyAmt(new BigDecimal("0.00"));
            loanReplan.setBreachAmt(new BigDecimal("0.00"));
            loanReplan.setGuaranteeAmt(new BigDecimal("100.00"));
            loanReplan.setConsultAmt(new BigDecimal("50.00"));
            loanReplan.setCustRepayStatus(RepayStatus.REPAID);
            loanReplan.setBankRepayStatus(RepayStatus.REPAID);
            loanReplan.setCreatedBy("system");
            loanReplan.setCreatedTime(LocalDateTime.now());
            loanReplanRepository.save(loanReplan);

            loanReplan.setCustRepayStatus(RepayStatus.REPAID);
            loanReplan.setBankRepayStatus(RepayStatus.REPAID);
            loanReplan.setUpdatedBy("admin");
            loanReplan.setUpdatedTime(LocalDateTime.now());
            loanReplanRepository.save(loanReplan);

            loanReplanRepository.findById(loanReplan.getId())
                    .ifPresent(lr -> System.out.println("LoanReplan 查询成功: " + lr.getCustRepayStatus()));
            loanReplanRepository.deleteById(loanReplan.getId());

            // 3.还款
            // CustomerRepayRecord
            // ====== CustomerRepayRecord ======
            CustomerRepayRecord customerRepayRecord = new CustomerRepayRecord();
            customerRepayRecord.setId(testId);
            customerRepayRecord.setLoanId("loan_" + testId);
            customerRepayRecord.setOuterRepayId("outer_repay_" + testId);
            customerRepayRecord.setChannel(BankChannel.CYBK);
            customerRepayRecord.setPeriod(1);
            customerRepayRecord.setRepayStatus(ProcessStatus.SUCCESS);
            customerRepayRecord.setTotalAmt(new BigDecimal("4583.33"));
            customerRepayRecord.setPrincipalAmt(new BigDecimal("4166.67"));
            customerRepayRecord.setInterestAmt(new BigDecimal("416.66"));
            customerRepayRecord.setPenaltyAmt(new BigDecimal("0.00"));
            customerRepayRecord.setPlatformPenaltyAmt(new BigDecimal("0.00"));
            customerRepayRecord.setGuaranteeAmt(new BigDecimal("100.00"));
            customerRepayRecord.setBreachAmt(new BigDecimal("0.00"));
            customerRepayRecord.setConsultAmt(new BigDecimal("50.00"));
            customerRepayRecord.setBankPenaltyAmt(new BigDecimal("0.00"));
            customerRepayRecord.setRepayType(RepayType.REPAY);
            customerRepayRecord.setRepayMode(RepayMode.ONLINE);
            customerRepayRecord.setRepayPurpose(RepayPurpose.CURRENT);
            customerRepayRecord.setRepayCategory(RepayCategory.BEFORE_CLAIM);
            customerRepayRecord.setPartRepayType(PartRepayType.NONE);
            customerRepayRecord.setReduceAmt(new BigDecimal("0.00"));
            customerRepayRecord.setAgreementNo("AGREEMENT_001");
            customerRepayRecord.setRepayBankCode("ICBC");
            customerRepayRecord.setRepayAcctNo("****************");
            customerRepayRecord.setRepayRelUser("测试用户");
            customerRepayRecord.setRepayRelPhone("***********");
            customerRepayRecord.setRepayRelCard("110101199001011234");
            customerRepayRecord.setPayOrderNo("PAY_ORDER_001");
            customerRepayRecord.setShareBankAmt(new BigDecimal("4000.00"));
            customerRepayRecord.setShareFlowAmt(new BigDecimal("500.00"));
            customerRepayRecord.setShareGuaranteeAmt(new BigDecimal("83.33"));
            customerRepayRecord.setShareRestAmt(new BigDecimal("0.00"));
            customerRepayRecord.setCreatedBy("system");
            customerRepayRecord.setCreatedTime(LocalDateTime.now());
            customerRepayRecordRepository.save(customerRepayRecord);

            customerRepayRecord.setRepayStatus(ProcessStatus.SUCCESS);
            customerRepayRecord.setRepayTime(LocalDateTime.now());
            customerRepayRecord.setBankSerial("BANK_SERIAL_001");
            customerRepayRecord.setTransferDate(LocalDate.now());
            customerRepayRecord.setUpdatedBy("admin");
            customerRepayRecord.setUpdatedTime(LocalDateTime.now());
            customerRepayRecordRepository.save(customerRepayRecord);

            customerRepayRecordRepository.findById(customerRepayRecord.getId())
                    .ifPresent(crr -> System.out.println("CustomerRepayRecord 查询成功: " + crr.getRepayStatus()));
            customerRepayRecordRepository.deleteById(customerRepayRecord.getId());

            // BankRepayRecord
            // ====== BankRepayRecord ======
            BankRepayRecord bankRepayRecord = new BankRepayRecord();
            bankRepayRecord.setId(testId);
            bankRepayRecord.setSysId("sys_" + testId);
            bankRepayRecord.setLoanId("loan_" + testId);
            bankRepayRecord.setChannel(BankChannel.CYBK);
            bankRepayRecord.setPeriod(1);
            bankRepayRecord.setRepayStatus(ProcessStatus.SUCCESS);
            bankRepayRecord.setTotalAmt(new BigDecimal("4000.00"));
            bankRepayRecord.setPrincipalAmt(new BigDecimal("4166.67"));
            bankRepayRecord.setInterestAmt(new BigDecimal("416.66"));
            bankRepayRecord.setPenaltyAmt(new BigDecimal("0.00"));
            bankRepayRecord.setPlatformPenaltyAmt(new BigDecimal("0.00"));
            bankRepayRecord.setGuaranteeAmt(new BigDecimal("83.33"));
            bankRepayRecord.setConsultAmt(new BigDecimal("0.00"));
            bankRepayRecord.setBreachAmt(new BigDecimal("0.00"));
            bankRepayRecord.setRepayType(RepayType.REPAY);
            bankRepayRecord.setRepayMode(RepayMode.ONLINE);
            bankRepayRecord.setRepayPurpose(RepayPurpose.CURRENT);
            bankRepayRecord.setRepayCategory(RepayCategory.BEFORE_CLAIM);
            bankRepayRecord.setPartRepayType(PartRepayType.NONE);
            bankRepayRecord.setAgreementNo("AGREEMENT_001");
            bankRepayRecord.setRepayBankCode("ICBC");
            bankRepayRecord.setRepayAcctNo("****************");
            bankRepayRecord.setRepayRelUser("测试用户");
            bankRepayRecord.setRepayRelPhone("***********");
            bankRepayRecord.setRepayRelCard("110101199001011234");
            bankRepayRecord.setPayOrderNo("PAY_ORDER_001");
            bankRepayRecord.setReduceAmount(new BigDecimal("0.00"));
            bankRepayRecord.setCouponAmt(new BigDecimal("0.00"));
            bankRepayRecord.setCreatedBy("system");
            bankRepayRecord.setCreatedTime(LocalDateTime.now());
            bankRepayRecordRepository.save(bankRepayRecord);

            bankRepayRecord.setRepayStatus(ProcessStatus.SUCCESS);
            bankRepayRecord.setRepayTime(LocalDateTime.now());
            bankRepayRecord.setBankSerial("BANK_SERIAL_001");
            bankRepayRecord.setBankBizId("BANK_BIZ_001");
            bankRepayRecord.setUpdatedBy("admin");
            bankRepayRecord.setUpdatedTime(LocalDateTime.now());
            bankRepayRecordRepository.save(bankRepayRecord);

            bankRepayRecordRepository.findById(bankRepayRecord.getId())
                    .ifPresent(brr -> System.out.println("BankRepayRecord 查询成功: " + brr.getRepayStatus()));
            bankRepayRecordRepository.deleteById(bankRepayRecord.getId());


            result = RestResult.success("1");
        } catch (Exception e) {
            result = RestResult.fail(ResultCode.BIZ_ERROR);
            System.err.println("CustomerRepayRecord CRUD测试失败: " + e.getMessage());
            return result;
        }
        return result;

    }
}

