package com.jinghang.capital.core.entity;


import com.jinghang.capital.core.enums.Education;
import com.jinghang.capital.core.enums.Gender;
import com.jinghang.capital.core.enums.Marriage;
import jakarta.persistence.Entity;
import jakarta.persistence.EnumType;
import jakarta.persistence.Enumerated;
import jakarta.persistence.Table;

import java.io.Serial;
import java.math.BigDecimal;
import java.time.LocalDate;

/**
 * 用户表
 */
@Entity
@Table(name = "account")
public class Account extends BaseEntity {

    @Serial
    private static final long serialVersionUID = 1586569934464751766L;

    /**
     * 姓名
     */
    private String name;
    /**
     * 身份证号码
     */
    private String certNo;
    /**
     * 手机号码
     */
    private String mobile;
    /**
     * 出生日期
     */
    private LocalDate birthDay;
    /**
     * 身份证地址
     */
    private String certAddress;
    /**
     * 签发机关
     */
    private String certSignOrg;
    /**
     * 身份证有效期起始
     */
    private LocalDate certValidStart;
    /**
     * 身份证有效期截至
     */
    private LocalDate certValidEnd;
    /**
     * 省行政区划
     */
    private String provinceCode;
    /**
     * 市行政区划
     */
    private String cityCode;
    /**
     * 区行政区划
     */
    private String districtCode;
    /**
     * 性别
     */
    @Enumerated(EnumType.STRING)
    private Gender gender;
    /**
     * 民族
     */
    private String nation;
    /**
     * 婚姻状况
     */
    @Enumerated(EnumType.STRING)
    private Marriage marriage;
    /**
     * 最高学历
     */
    @Enumerated(EnumType.STRING)
    private Education education;
    /**
     * A卡分
     */
    private String acardScore;
    /**
     * B卡分
     */
    private String bcardScore;
    /**
     * 月收入
     */
    private Integer income;
    /**
     * 居住地址
     */
    private String livingAddress;
    /**
     * 居住省份
     */
    private String livingProvinceCode;
    /**
     * 居住市
     */
    private String livingCityCode;
    /**
     * 居住区
     */
    private String livingDistrictCode;
    /**
     * 街道
     */
    private String livingStreet;
    /**
     * 工作单位
     */
    private String unit;
    /**
     * 工作单位地址
     */
    private String unitAddress;
    /**
     * 工作单位省份
     */
    private String unitProvinceCode;
    /**
     * 工作单位市
     */
    private String unitCityCode;
    /**
     * 工作单位区
     */
    private String unitDistrictCode;
    /**
     * 街道
     */
    private String unitStreet;
    /**
     * 行业
     */
    private String industry;
    /**
     * 职业
     */
    private String position;
    /**
     * 邮编
     */
    private String email;
    /**
     * 人脸识别通道
     */
    private String faceChannel;
    /**
     * 人脸识别时间
     */
    private String faceTime;
    /**
     * 人脸识别分数
     */
    private BigDecimal faceScore;

    public String getLivingStreet() {
        return livingStreet;
    }

    public void setLivingStreet(String livingStreet) {
        this.livingStreet = livingStreet;
    }

    public String getUnitStreet() {
        return unitStreet;
    }

    public void setUnitStreet(String unitStreet) {
        this.unitStreet = unitStreet;
    }

    public String getIndustry() {
        return industry;
    }

    public void setIndustry(String industry) {
        this.industry = industry;
    }

    public String getPosition() {
        return position;
    }

    public void setPosition(String position) {
        this.position = position;
    }

    public String getEmail() {
        return email;
    }

    public void setEmail(String email) {
        this.email = email;
    }

    public String getFaceChannel() {
        return faceChannel;
    }

    public void setFaceChannel(String faceChannel) {
        this.faceChannel = faceChannel;
    }

    public String getFaceTime() {
        return faceTime;
    }

    public void setFaceTime(String faceTime) {
        this.faceTime = faceTime;
    }

    public BigDecimal getFaceScore() {
        return faceScore;
    }

    public void setFaceScore(BigDecimal faceScore) {
        this.faceScore = faceScore;
    }


    public String getAcardScore() {
        return acardScore;
    }

    public void setAcardScore(String acardScore) {
        this.acardScore = acardScore;
    }

    public String getBcardScore() {
        return bcardScore;
    }

    public void setBcardScore(String bcardScore) {
        this.bcardScore = bcardScore;
    }

    /**
     * 姓名
     */
    public String getName() {
        return this.name;
    }

    /**
     * 姓名
     */
    public void setName(String name) {
        this.name = name;
    }

    /**
     * 身份证号码
     */
    public String getCertNo() {
        return this.certNo;
    }

    /**
     * 身份证号码
     */
    public void setCertNo(String certNo) {
        this.certNo = certNo;
    }

    /**
     * 手机号码
     */
    public String getMobile() {
        return this.mobile;
    }

    /**
     * 手机号码
     */
    public void setMobile(String mobile) {
        this.mobile = mobile;
    }

    /**
     * 出生日期
     */
    public LocalDate getBirthDay() {
        return this.birthDay;
    }

    /**
     * 出生日期
     */
    public void setBirthDay(LocalDate birthDay) {
        this.birthDay = birthDay;
    }

    /**
     * 身份证地址
     */
    public String getCertAddress() {
        return this.certAddress;
    }

    /**
     * 身份证地址
     */
    public void setCertAddress(String certAddress) {
        this.certAddress = certAddress;
    }

    /**
     * 签发机关
     */
    public String getCertSignOrg() {
        return this.certSignOrg;
    }

    /**
     * 签发机关
     */
    public void setCertSignOrg(String certSignOrg) {
        this.certSignOrg = certSignOrg;
    }

    /**
     * 身份证有效期起始
     */
    public LocalDate getCertValidStart() {
        return this.certValidStart;
    }

    /**
     * 身份证有效期起始
     */
    public void setCertValidStart(LocalDate certValidStart) {
        this.certValidStart = certValidStart;
    }

    /**
     * 身份证有效期截至
     */
    public LocalDate getCertValidEnd() {
        return this.certValidEnd;
    }

    /**
     * 身份证有效期截至
     */
    public void setCertValidEnd(LocalDate certValidEnd) {
        this.certValidEnd = certValidEnd;
    }

    /**
     * 省行政区划
     */
    public String getProvinceCode() {
        return this.provinceCode;
    }

    /**
     * 省行政区划
     */
    public void setProvinceCode(String provinceCode) {
        this.provinceCode = provinceCode;
    }

    /**
     * 市行政区划
     */
    public String getCityCode() {
        return this.cityCode;
    }

    /**
     * 市行政区划
     */
    public void setCityCode(String cityCode) {
        this.cityCode = cityCode;
    }

    /**
     * 区行政区划
     */
    public String getDistrictCode() {
        return this.districtCode;
    }

    /**
     * 区行政区划
     */
    public void setDistrictCode(String districtCode) {
        this.districtCode = districtCode;
    }

    /**
     * 性别
     */
    public Gender getGender() {
        return this.gender;
    }

    /**
     * 性别
     */
    public void setGender(Gender gender) {
        this.gender = gender;
    }

    /**
     * 民族
     */
    public String getNation() {
        return this.nation;
    }

    /**
     * 民族
     */
    public void setNation(String nation) {
        this.nation = nation;
    }

    public Marriage getMarriage() {
        return marriage;
    }

    public void setMarriage(Marriage marriage) {
        this.marriage = marriage;
    }

    /**
     * 最高学历
     */
    public Education getEducation() {
        return this.education;
    }

    /**
     * 最高学历
     */
    public void setEducation(Education education) {
        this.education = education;
    }

    /**
     * 月收入
     */
    public Integer getIncome() {
        return this.income;
    }

    /**
     * 月收入
     */
    public void setIncome(Integer income) {
        this.income = income;
    }

    /**
     * 居住地址
     */
    public String getLivingAddress() {
        return this.livingAddress;
    }

    /**
     * 居住地址
     */
    public void setLivingAddress(String livingAddress) {
        this.livingAddress = livingAddress;
    }

    /**
     * 居住省份
     */
    public String getLivingProvinceCode() {
        return this.livingProvinceCode;
    }

    /**
     * 居住省份
     */
    public void setLivingProvinceCode(String livingProvinceCode) {
        this.livingProvinceCode = livingProvinceCode;
    }

    /**
     * 居住市
     */
    public String getLivingCityCode() {
        return this.livingCityCode;
    }

    /**
     * 居住市
     */
    public void setLivingCityCode(String livingCityCode) {
        this.livingCityCode = livingCityCode;
    }

    /**
     * 居住区
     */
    public String getLivingDistrictCode() {
        return this.livingDistrictCode;
    }

    /**
     * 居住区
     */
    public void setLivingDistrictCode(String livingDistrictCode) {
        this.livingDistrictCode = livingDistrictCode;
    }

    /**
     * 工作单位
     */
    public String getUnit() {
        return this.unit;
    }

    /**
     * 工作单位
     */
    public void setUnit(String unit) {
        this.unit = unit;
    }

    /**
     * 工作单位地址
     */
    public String getUnitAddress() {
        return this.unitAddress;
    }

    /**
     * 工作单位地址
     */
    public void setUnitAddress(String unitAddress) {
        this.unitAddress = unitAddress;
    }

    /**
     * 工作单位省份
     */
    public String getUnitProvinceCode() {
        return this.unitProvinceCode;
    }

    /**
     * 工作单位省份
     */
    public void setUnitProvinceCode(String unitProvinceCode) {
        this.unitProvinceCode = unitProvinceCode;
    }

    /**
     * 工作单位市
     */
    public String getUnitCityCode() {
        return this.unitCityCode;
    }

    /**
     * 工作单位市
     */
    public void setUnitCityCode(String unitCityCode) {
        this.unitCityCode = unitCityCode;
    }

    /**
     * 工作单位区
     */
    public String getUnitDistrictCode() {
        return this.unitDistrictCode;
    }

    /**
     * 工作单位区
     */
    public void setUnitDistrictCode(String unitDistrictCode) {
        this.unitDistrictCode = unitDistrictCode;
    }


    @Override
    public String prefix() {
        return "A";
    }
}
