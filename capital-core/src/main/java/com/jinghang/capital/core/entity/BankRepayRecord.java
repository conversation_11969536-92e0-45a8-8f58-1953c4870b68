package com.jinghang.capital.core.entity;

import com.jinghang.capital.api.dto.repay.PartRepayType;
import com.jinghang.capital.api.dto.repay.RepayCategory;
import com.jinghang.capital.core.enums.BankChannel;
import com.jinghang.capital.core.enums.ProcessStatus;
import com.jinghang.capital.core.enums.RepayMode;
import com.jinghang.capital.core.enums.RepayPurpose;
import com.jinghang.capital.core.enums.RepayType;

import jakarta.persistence.Entity;
import jakarta.persistence.EnumType;
import jakarta.persistence.Enumerated;
import jakarta.persistence.Table;

import java.io.Serial;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 对资还款记录表
 */
@Entity
@Table(name = "bank_repay_record")
public class BankRepayRecord extends BaseEntity {
    @Serial
    private static final long serialVersionUID = 198238222541262798L;

    /**
     * 对客
     */
    private String sysId;

    /**
     * 借款id
     */
    private String loanId;
    /**
     * 资方渠道
     */
    @Enumerated(EnumType.STRING)
    private BankChannel channel;
    /**
     * 期数
     */
    private Integer period;
    /**
     * 还款状态
     */
    @Enumerated(EnumType.STRING)
    private ProcessStatus repayStatus;
    /**
     * 还款时间
     */
    private LocalDateTime repayTime;
    /**
     * 总金额
     */
    private BigDecimal totalAmt;
    /**
     * 本金
     */
    private BigDecimal principalAmt;
    /**
     * 利息
     */
    private BigDecimal interestAmt;
    /**
     * 罚息
     */
    private BigDecimal penaltyAmt;
    /**
     * 平台罚息（对客罚息-对资罚息）
     */
    private BigDecimal platformPenaltyAmt;
    /**
     * 融担费用
     */
    private BigDecimal guaranteeAmt;
    /**
     * 咨询费
     */
    private BigDecimal consultAmt;
    /**
     * 违约金
     */
    private BigDecimal breachAmt;

    /**
     * 还款类型
     */
    @Enumerated(EnumType.STRING)
    private RepayType repayType;
    /**
     * 还款模式
     */
    @Enumerated(EnumType.STRING)
    private RepayMode repayMode;
    /**
     * 还款模式
     */
    @Enumerated(EnumType.STRING)
    private RepayPurpose repayPurpose;

    /**
     * 还款分类
     */
    @Enumerated(EnumType.STRING)
    private RepayCategory repayCategory;

    /**
     * 部分还款类型
     */
    @Enumerated(EnumType.STRING)
    private PartRepayType partRepayType;


    /**
     * 银行扣款流水号
     */
    private String bankSerial;

    /**
     * 请求银行侧业务编号
     */
    private String bankBizId;

    /**
     * 扣款协议号 - 线上扣款
     */
    private String agreementNo;
    /**
     * 还款银行编码 - 线上扣款
     */
    private String repayBankCode;
    /**
     * 还款卡号 - 线上扣款
     */
    private String repayAcctNo;
    /**
     * 还款方用户名 - 线上扣款
     */
    private String repayRelUser;
    /**
     * 还款方手机号 - 线上扣款
     */
    private String repayRelPhone;
    /**
     * 还款方身份证号 - 线上扣款
     */
    private String repayRelCard;

    /**
     * 代扣流水号
     */
    private String payOrderNo;

    /**
     * 资方减免金额
     */
    private BigDecimal reduceAmount;

    /**
     * 还款优惠劵金额(360活动减免金额-对客咨询费)
     */
    private BigDecimal couponAmt;

    /**
     * 扣款渠道id
     */
    private String chargeChannelId;

    /**
     * 关联的项目唯一编码
     */
    private String projectCode;

    public String getProjectCode() {
        return projectCode;
    }

    public void setProjectCode(String projectCode) {
        this.projectCode = projectCode;
    }

    public String getPayOrderNo() {
        return payOrderNo;
    }

    public void setPayOrderNo(String payOrderNo) {
        this.payOrderNo = payOrderNo;
    }

    public String getSysId() {
        return sysId;
    }

    public void setSysId(String sysId) {
        this.sysId = sysId;
    }

    /**
     * 借款id
     */
    public String getLoanId() {
        return this.loanId;
    }

    /**
     * 借款id
     */
    public void setLoanId(String loanId) {
        this.loanId = loanId;
    }

    public BankChannel getChannel() {
        return channel;
    }

    public void setChannel(BankChannel channel) {
        this.channel = channel;
    }

    /**
     * 期数
     */
    public Integer getPeriod() {
        return this.period;
    }

    /**
     * 期数
     */
    public void setPeriod(Integer period) {
        this.period = period;
    }

    public ProcessStatus getRepayStatus() {
        return repayStatus;
    }

    public void setRepayStatus(ProcessStatus repayStatus) {
        this.repayStatus = repayStatus;
    }

    /**
     * 还款时间
     */
    public LocalDateTime getRepayTime() {
        return this.repayTime;
    }

    /**
     * 还款时间
     */
    public void setRepayTime(LocalDateTime repayTime) {
        this.repayTime = repayTime;
    }

    /**
     * 总金额
     */
    public BigDecimal getTotalAmt() {
        return this.totalAmt;
    }

    /**
     * 总金额
     */
    public void setTotalAmt(BigDecimal totalAmt) {
        this.totalAmt = totalAmt;
    }

    /**
     * 本金
     */
    public BigDecimal getPrincipalAmt() {
        return this.principalAmt;
    }

    /**
     * 本金
     */
    public void setPrincipalAmt(BigDecimal principalAmt) {
        this.principalAmt = principalAmt;
    }

    /**
     * 利息
     */
    public BigDecimal getInterestAmt() {
        return this.interestAmt;
    }

    /**
     * 利息
     */
    public void setInterestAmt(BigDecimal interestAmt) {
        this.interestAmt = interestAmt;
    }

    /**
     * 罚息
     */
    public BigDecimal getPenaltyAmt() {
        return this.penaltyAmt;
    }

    /**
     * 罚息
     */
    public void setPenaltyAmt(BigDecimal penaltyAmt) {
        this.penaltyAmt = penaltyAmt;
    }

    public BigDecimal getPlatformPenaltyAmt() {
        return platformPenaltyAmt;
    }

    public void setPlatformPenaltyAmt(BigDecimal platformPenaltyAmt) {
        this.platformPenaltyAmt = platformPenaltyAmt;
    }

    public BigDecimal getGuaranteeAmt() {
        return guaranteeAmt;
    }

    public void setGuaranteeAmt(BigDecimal guaranteeAmt) {
        this.guaranteeAmt = guaranteeAmt;
    }

    public BigDecimal getConsultAmt() {
        return consultAmt;
    }

    public void setConsultAmt(BigDecimal consultAmt) {
        this.consultAmt = consultAmt;
    }

    /**
     * 违约金
     */
    public BigDecimal getBreachAmt() {
        return this.breachAmt;
    }

    /**
     * 违约金
     */
    public void setBreachAmt(BigDecimal breachAmt) {
        this.breachAmt = breachAmt;
    }

    /**
     * 是否逾期
     */
    private String isOverdue;

    public String getIsOverdue() {
        return isOverdue;
    }

    public void setIsOverdue(String isOverdue) {
        this.isOverdue = isOverdue;
    }

    public RepayType getRepayType() {
        return repayType;
    }

    public void setRepayType(RepayType repayType) {
        this.repayType = repayType;
    }

    public RepayMode getRepayMode() {
        return repayMode;
    }

    public void setRepayMode(RepayMode repayMode) {
        this.repayMode = repayMode;
    }

    public RepayPurpose getRepayPurpose() {
        return repayPurpose;
    }

    public void setRepayPurpose(RepayPurpose repayPurpose) {
        this.repayPurpose = repayPurpose;
    }

    /**
     * 银行扣款流水号
     */
    public String getBankSerial() {
        return this.bankSerial;
    }

    /**
     * 银行扣款流水号
     */
    public void setBankSerial(String bankSerial) {
        this.bankSerial = bankSerial;
    }

    public String getBankBizId() {
        return bankBizId;
    }

    public void setBankBizId(String bankBizId) {
        this.bankBizId = bankBizId;
    }

    public String getAgreementNo() {
        return agreementNo;
    }

    public void setAgreementNo(String agreementNo) {
        this.agreementNo = agreementNo;
    }

    public String getRepayBankCode() {
        return repayBankCode;
    }

    public void setRepayBankCode(String repayBankCode) {
        this.repayBankCode = repayBankCode;
    }

    public String getRepayAcctNo() {
        return repayAcctNo;
    }

    public void setRepayAcctNo(String repayAcctNo) {
        this.repayAcctNo = repayAcctNo;
    }

    public String getRepayRelUser() {
        return repayRelUser;
    }

    public void setRepayRelUser(String repayRelUser) {
        this.repayRelUser = repayRelUser;
    }

    public String getRepayRelPhone() {
        return repayRelPhone;
    }

    public void setRepayRelPhone(String repayRelPhone) {
        this.repayRelPhone = repayRelPhone;
    }

    public String getRepayRelCard() {
        return repayRelCard;
    }

    public void setRepayRelCard(String repayRelCard) {
        this.repayRelCard = repayRelCard;
    }

    public BigDecimal getReduceAmount() {
        return reduceAmount;
    }

    public void setReduceAmount(BigDecimal reduceAmount) {
        this.reduceAmount = reduceAmount;
    }

    public BigDecimal getCouponAmt() {
        return couponAmt;
    }

    public void setCouponAmt(BigDecimal couponAmt) {
        this.couponAmt = couponAmt;
    }

    public String getChargeChannelId() {
        return chargeChannelId;
    }

    public void setChargeChannelId(String chargeChannelId) {
        this.chargeChannelId = chargeChannelId;
    }

    public RepayCategory getRepayCategory() {
        return repayCategory;
    }

    public void setRepayCategory(RepayCategory repayCategory) {
        this.repayCategory = repayCategory;
    }

    public PartRepayType getPartRepayType() {
        return partRepayType;
    }

    public void setPartRepayType(PartRepayType partRepayType) {
        this.partRepayType = partRepayType;
    }

    @Override
    public String prefix() {
        return "BRR";
    }
}
