package com.jinghang.capital.core.entity;

import jakarta.persistence.Entity;
import jakarta.persistence.Id;
import jakarta.persistence.Table;

import java.io.Serial;
import java.io.Serializable;


/**
 * 长银直连 放款支持银行列表
 */
@Entity
@Table(name = "cybk_bank_list")
public class CYBKBankList implements Serializable {


    @Serial
    private static final long serialVersionUID = 825635379549238786L;

    @Id
    private Integer id;

    /**
     * 银行名称缩写
     */
    private String abbr;

    /**
     * 银行名称
     */
    private String name;
    /**
     * 银行名称
     */
    private String bankCode;
    /**
     * 长银银行编码
     */
    private String cycfcCode;

    private String cycfcName;

    public String getBankCode() {
        return bankCode;
    }

    public void setBankCode(String bankCode) {
        this.bankCode = bankCode;
    }

    public String getCycfcCode() {
        return cycfcCode;
    }

    public void setCycfcCode(String cycfcCode) {
        this.cycfcCode = cycfcCode;
    }

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public String getAbbr() {
        return abbr;
    }

    public void setAbbr(String abbr) {
        this.abbr = abbr;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getCycfcName() {
        return cycfcName;
    }

    public void setCycfcName(String cycfcName) {
        this.cycfcName = cycfcName;
    }
}
