package com.jinghang.capital.core.entity;

import jakarta.persistence.*;

import java.time.LocalDateTime;

/**
 * @公司 中数金智(上海)有限公司
 * @作者 Mr.sandman
 * @时间 2025/06/25 15:00
 */
@Entity
@Table(name = "cybk_pre_credit")
public class CYBKPreCredit {

  /**
   * 主键id
   */
  @Id
  @GeneratedValue(strategy = GenerationType.IDENTITY)
  private Long id;

  /**
   * 身份证号
   */
  private String idNo;

  /**
   * 手机号
   */
  private String mobile;

  /**
   * 产品码值
   */
  private String productCode;

  /**
   * 准入状态
   * 01 处理中
   * 02 拒绝
   * 03 额度类通过
   * 04 非额度类通过
   */
  private String result;

  /**
   * 在贷标识
   * Y:有在贷
   * N:无在贷
   */
  private String isStock;

  /**
   * 风控码值
   */
  private String outRiskCode;

  /**
   * 风控原因
   */
  private String outRiskMsg;

  /**
   * 创建时间
   */
  private LocalDateTime createTime;

  public Long getId() {
    return id;
  }

  public void setId( Long id ) {
    this.id = id;
  }

  public String getIdNo() {
    return idNo;
  }

  public void setIdNo( String idNo ) {
    this.idNo = idNo;
  }

  public String getMobile() {
    return mobile;
  }

  public void setMobile( String mobile ) {
    this.mobile = mobile;
  }

  public String getProductCode() {
    return productCode;
  }

  public void setProductCode( String productCode ) {
    this.productCode = productCode;
  }

  public String getResult() {
    return result;
  }

  public void setResult( String result ) {
    this.result = result;
  }

  public String getIsStock() {
    return isStock;
  }

  public void setIsStock( String isStock ) {
    this.isStock = isStock;
  }

  public String getOutRiskCode() {
    return outRiskCode;
  }

  public void setOutRiskCode( String outRiskCode ) {
    this.outRiskCode = outRiskCode;
  }

  public String getOutRiskMsg() {
    return outRiskMsg;
  }

  public void setOutRiskMsg( String outRiskMsg ) {
    this.outRiskMsg = outRiskMsg;
  }

  public LocalDateTime getCreateTime() {
    return createTime;
  }

  public void setCreateTime( LocalDateTime createTime ) {
    this.createTime = createTime;
  }
}
