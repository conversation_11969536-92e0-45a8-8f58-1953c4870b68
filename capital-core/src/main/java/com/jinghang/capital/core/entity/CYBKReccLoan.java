package com.jinghang.capital.core.entity;

import jakarta.persistence.Entity;
import jakarta.persistence.Table;

import java.io.Serial;
import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDate;

/**
 * <p>
 * 长银直连放款对账文件
 * </p>
 *
 * <AUTHOR>
 * @since 2023-05-21
 */
@Entity
@Table(name = "cybk_recc_loan")
public class CYBKReccLoan extends BaseEntity implements Serializable {


    @Serial
    private static final long serialVersionUID = 3174434490597981310L;

    /**
     * recc_id
     */
    private String reccId;

    private String outAppSeq;
    private String custId;
    private String applyDt;
    private String applSeq;
    private String contNo;
    private String basicIntRat;

    /**
     * 资金方借据
     */
    private String loanNo;

    /**
     * 借据
     */
    private String sysId;

    /**
     * 放款金额
     */
    private BigDecimal amount;

    /**
     * 期限
     */
    private Integer period;

    /**
     * 放款时间
     */
    private LocalDate loanTime;

    /**
     * 放款状态
     */
    private String status;

    /**
     * 资方
     */
    private String channel;



    /**
     * 对账状态
     */
    private String reccStatus;

    public String getReccId() {
        return reccId;
    }

    public void setReccId(String reccId) {
        this.reccId = reccId;
    }

    public String getOutAppSeq() {
        return outAppSeq;
    }

    public void setOutAppSeq(String outAppSeq) {
        this.outAppSeq = outAppSeq;
    }

    public String getCustId() {
        return custId;
    }

    public void setCustId(String custId) {
        this.custId = custId;
    }

    public String getApplyDt() {
        return applyDt;
    }

    public void setApplyDt(String applyDt) {
        this.applyDt = applyDt;
    }

    public String getApplSeq() {
        return applSeq;
    }

    public void setApplSeq(String applSeq) {
        this.applSeq = applSeq;
    }

    public String getContNo() {
        return contNo;
    }

    public void setContNo(String contNo) {
        this.contNo = contNo;
    }

    public String getBasicIntRat() {
        return basicIntRat;
    }

    public void setBasicIntRat(String basicIntRat) {
        this.basicIntRat = basicIntRat;
    }

    public String getLoanNo() {
        return loanNo;
    }

    public void setLoanNo(String loanNo) {
        this.loanNo = loanNo;
    }

    public String getSysId() {
        return sysId;
    }

    public void setSysId(String sysId) {
        this.sysId = sysId;
    }

    public BigDecimal getAmount() {
        return amount;
    }

    public void setAmount(BigDecimal amount) {
        this.amount = amount;
    }

    public Integer getPeriod() {
        return period;
    }

    public void setPeriod(Integer period) {
        this.period = period;
    }

    public LocalDate getLoanTime() {
        return loanTime;
    }

    public void setLoanTime(LocalDate loanTime) {
        this.loanTime = loanTime;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public String getChannel() {
        return channel;
    }

    public void setChannel(String channel) {
        this.channel = channel;
    }

    public String getReccStatus() {
        return reccStatus;
    }

    public void setReccStatus(String reccStatus) {
        this.reccStatus = reccStatus;
    }
}
