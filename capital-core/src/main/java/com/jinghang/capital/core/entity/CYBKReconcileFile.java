package com.jinghang.capital.core.entity;

import jakarta.persistence.Entity;
import jakarta.persistence.Table;

import java.io.Serial;
import java.io.Serializable;
import java.time.LocalDate;

/**
 * <p>
 * 长银直连对账文件
 * </p>
 *
 * <AUTHOR>
 * @since 2023-05-21
 */
@Entity
@Table(name = "cybk_reconcile_file")
public class CYBKReconcileFile extends BaseEntity implements Serializable {

    @Serial
    private static final long serialVersionUID = -4591845863553509366L;

    /**
     * 产品
     */
    private String product;

    /**
     * 资方
     */
    private String channel;

    /**
     * 对账文件类型
     */
    private String reccType;

    /**
     * 文件名称
     */
    private String fileName;

    /**
     * 文件日期
     */
    private LocalDate fileDate;

    /**
     * 对账状态
     */
    private String reccState;

    /**
     * 来源文件bucket
     */
    private String ossBucket;

    /**
     * 来源文件key
     */
    private String ossKey;

    /**
     * 来源文件oss
     */
    private String ossUrl;

    /**
     * 对账日期
     */
    private LocalDate reccDate;

    public String getProduct() {
        return product;
    }

    public void setProduct(String product) {
        this.product = product;
    }

    public String getChannel() {
        return channel;
    }

    public void setChannel(String channel) {
        this.channel = channel;
    }

    public String getReccType() {
        return reccType;
    }

    public void setReccType(String reccType) {
        this.reccType = reccType;
    }

    public String getFileName() {
        return fileName;
    }

    public void setFileName(String fileName) {
        this.fileName = fileName;
    }

    public LocalDate getFileDate() {
        return fileDate;
    }

    public void setFileDate(LocalDate fileDate) {
        this.fileDate = fileDate;
    }

    public String getReccState() {
        return reccState;
    }

    public void setReccState(String reccState) {
        this.reccState = reccState;
    }

    public String getOssBucket() {
        return ossBucket;
    }

    public void setOssBucket(String ossBucket) {
        this.ossBucket = ossBucket;
    }

    public String getOssKey() {
        return ossKey;
    }

    public void setOssKey(String ossKey) {
        this.ossKey = ossKey;
    }

    public String getOssUrl() {
        return ossUrl;
    }

    public void setOssUrl(String ossUrl) {
        this.ossUrl = ossUrl;
    }

    public LocalDate getReccDate() {
        return reccDate;
    }

    public void setReccDate(LocalDate reccDate) {
        this.reccDate = reccDate;
    }

}
