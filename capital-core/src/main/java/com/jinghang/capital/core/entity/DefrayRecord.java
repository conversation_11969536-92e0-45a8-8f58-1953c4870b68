package com.jinghang.capital.core.entity;


import com.jinghang.capital.api.dto.DefrayType;
import com.jinghang.capital.api.dto.ProtocolChannel;
import com.jinghang.capital.core.enums.BankChannel;
import com.jinghang.capital.core.enums.ProcessStatus;


import jakarta.persistence.Entity;
import jakarta.persistence.EnumType;
import jakarta.persistence.Enumerated;
import jakarta.persistence.Table;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * 代付记录表
 */
@Entity
@Table(name = "defray_record")
public class DefrayRecord extends BaseEntity {

    /**
     * 资方渠道
     */
    @Enumerated(EnumType.STRING)
    private BankChannel channel;

    /**
     * 数据日期
     */
    private LocalDate recordDate;

    /**
     * 申请id
     */
    private String applyId;

    /**
     * 申请时间
     */
    private LocalDateTime applyTime;

    /**
     * 费用类型
     */
    @Enumerated(EnumType.STRING)
    private DefrayType defrayType;

    /**
     * 代付状态
     */
    @Enumerated(EnumType.STRING)
    private ProcessStatus status;

    /**
     * 代付时间
     */
    private LocalDateTime transTime;

    /**
     * 代付流水号
     */
    private String transNo;

    /**
     * 转账金额
     */
    private BigDecimal transMoney;

    /**
     * 收款人姓名
     */
    private String toAccName;

    /**
     * 收款人银行账号
     */
    private String toAccNo;

    /**
     * 收款人开户行机构名
     */
    private String toAccDept;

    /**
     * 收款人开户行
     */
    private String toAccBankName;

    /**
     * 出款方户名
     */
    private String defrayAccName;

    /**
     * 商户号
     */
    private String merchantId;

    /**
     * 代付渠道
     */
    @Enumerated(EnumType.STRING)
    private ProtocolChannel payChannel;

    public BankChannel getChannel() {
        return channel;
    }

    public void setChannel(BankChannel channel) {
        this.channel = channel;
    }

    public LocalDateTime getApplyTime() {
        return applyTime;
    }

    public void setApplyTime(LocalDateTime applyTime) {
        this.applyTime = applyTime;
    }

    public ProcessStatus getStatus() {
        return status;
    }

    public void setStatus(ProcessStatus status) {
        this.status = status;
    }

    public LocalDateTime getTransTime() {
        return transTime;
    }

    public void setTransTime(LocalDateTime transTime) {
        this.transTime = transTime;
    }

    public String getTransNo() {
        return transNo;
    }

    public void setTransNo(String transNo) {
        this.transNo = transNo;
    }

    public BigDecimal getTransMoney() {
        return transMoney;
    }

    public void setTransMoney(BigDecimal transMoney) {
        this.transMoney = transMoney;
    }

    public String getToAccName() {
        return toAccName;
    }

    public void setToAccName(String toAccName) {
        this.toAccName = toAccName;
    }

    public String getToAccNo() {
        return toAccNo;
    }

    public void setToAccNo(String toAccNo) {
        this.toAccNo = toAccNo;
    }

    public String getToAccDept() {
        return toAccDept;
    }

    public void setToAccDept(String toAccDept) {
        this.toAccDept = toAccDept;
    }

    public String getToAccBankName() {
        return toAccBankName;
    }

    public void setToAccBankName(String toAccBankName) {
        this.toAccBankName = toAccBankName;
    }

    public ProtocolChannel getPayChannel() {
        return payChannel;
    }

    public void setPayChannel(ProtocolChannel payChannel) {
        this.payChannel = payChannel;
    }

    public DefrayType getDefrayType() {
        return defrayType;
    }

    public void setDefrayType(DefrayType defrayType) {
        this.defrayType = defrayType;
    }

    public String getApplyId() {
        return applyId;
    }

    public void setApplyId(String applyId) {
        this.applyId = applyId;
    }

    public LocalDate getRecordDate() {
        return recordDate;
    }

    public void setRecordDate(LocalDate recordDate) {
        this.recordDate = recordDate;
    }

    public String getDefrayAccName() {
        return defrayAccName;
    }

    public void setDefrayAccName(String defrayAccName) {
        this.defrayAccName = defrayAccName;
    }

    public String getMerchantId() {
        return merchantId;
    }

    public void setMerchantId(String merchantId) {
        this.merchantId = merchantId;
    }
}
