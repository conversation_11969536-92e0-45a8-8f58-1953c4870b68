package com.jinghang.capital.core.entity;



import com.jinghang.capital.core.enums.BankChannel;
import com.jinghang.capital.core.enums.DownloadFileStatusEnum;
import com.jinghang.capital.core.enums.FileType;
import jakarta.persistence.Entity;
import jakarta.persistence.EnumType;
import jakarta.persistence.Enumerated;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;
import jakarta.persistence.Id;
import jakarta.persistence.Table;

import java.io.Serial;
import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * @ClassName LhrlDownloadFileLog
 * <AUTHOR>
 * @Description 资方接口文件下载日志
 * @Date 2024/1/8 18:54
 * @Version v1.0
 **/
@Entity
@Table(name = "download_file_log")
public class DownloadFileLog implements Serializable {
    @Serial
    private static final long serialVersionUID = 3103680217707995256L;
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;
    private String bizId;

    @Enumerated(EnumType.STRING)
    private BankChannel bankChannel;
    @Enumerated(EnumType.STRING)
    private FileType fileType;
    @Enumerated(EnumType.STRING)
    private DownloadFileStatusEnum status;
    private String remark;
    private String serialNo;
    private LocalDateTime createTime;
    private LocalDateTime updateTime;

    public String getSerialNo() {
        return serialNo;
    }

    public void setSerialNo(String serialNo) {
        this.serialNo = serialNo;
    }

    public BankChannel getBankChannel() {
        return bankChannel;
    }

    public void setBankChannel(BankChannel bankChannel) {
        this.bankChannel = bankChannel;
    }

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getBizId() {
        return bizId;
    }

    public void setBizId(String bizId) {
        this.bizId = bizId;
    }

    public FileType getFileType() {
        return fileType;
    }

    public void setFileType(FileType type) {
        this.fileType = type;
    }

    public DownloadFileStatusEnum getStatus() {
        return status;
    }

    public void setStatus(DownloadFileStatusEnum status) {
        this.status = status;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public LocalDateTime getCreateTime() {
        return createTime;
    }

    public void setCreateTime(LocalDateTime creationTime) {
        this.createTime = creationTime;
    }

    public LocalDateTime getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(LocalDateTime updateTime) {
        this.updateTime = updateTime;
    }
}
