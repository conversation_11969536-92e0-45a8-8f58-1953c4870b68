package com.jinghang.capital.core.entity;

import jakarta.persistence.Entity;
import jakarta.persistence.Table;

import java.io.Serial;

/**
 * 湖消上传蚂蚁借据文件记录表
 *
 * @Author: Lior
 * @CreateTime: 2025/7/8 19:26
 */
@Entity
@Table(name = "hxbk_ant_file_log")
public class HXBKAntFileLog extends BaseEntity {

    @Serial
    private static final long serialVersionUID = 7958567977554320790L;

    /**
     * 授信id
     */
    private String creditId;

    /**
     * 文件路径SFTP地址
     */
    private String filePath;

    /**
     * 文件名称
     */
    private String fileName;

    /**
     * 备注
     */
    private String remark;

    public String getCreditId() {
        return creditId;
    }

    public void setCreditId(String creditId) {
        this.creditId = creditId;
    }

    public String getFilePath() {
        return filePath;
    }

    public void setFilePath(String filePath) {
        this.filePath = filePath;
    }

    public String getFileName() {
        return fileName;
    }

    public void setFileName(String fileName) {
        this.fileName = fileName;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }
}
