package com.jinghang.capital.core.entity;

import jakarta.persistence.Entity;
import jakarta.persistence.Table;

import java.io.Serial;
import java.time.LocalDate;

/**
 * 门店
 */
@Entity
@Table(name = "shop")
public class Shop extends BaseEntity {
    @Serial
    private static final long serialVersionUID = 6977290404702731133L;

    /**
     * 门店名称
     */
    private String name;
    /**
     * 组织机构代码
     */
    private String unifiedCode;
    /**
     * 登记地址
     */
    private String licAddress;
    /**
     * 省代码
     */
    private String licProvinceCode;
    /**
     * 市代码
     */
    private String licCityCode;
    /**
     * 区代码
     */
    private String licDistrictCode;
    /**
     * 营业执照有效期开始
     */
    private LocalDate licStart;
    /**
     * 营业执照有效期结束
     */
    private LocalDate licEnd;
    /**
     * 实际营业地址
     */
    private String realAddress;
    /**
     * 实际省代码
     */
    private String realProvinceCode;
    /**
     * 实际市代码
     */
    private String realCityCode;
    /**
     * 实际区代码
     */
    private String realDistrictCode;
    /**
     * 法人姓名
     */
    private String legalName;
    /**
     * 法人手机
     */
    private String legalMobile;
    /**
     * 联系人姓名
     */
    private String linkmanName;
    /**
     * 联系人手机
     */
    private String linkmanMobile;


    /**
     * 门店名称
     */
    public String getName() {
        return this.name;
    }

    /**
     * 门店名称
     */
    public void setName(String name) {
        this.name = name;
    }

    /**
     * 组织机构代码
     */
    public String getUnifiedCode() {
        return this.unifiedCode;
    }

    /**
     * 组织机构代码
     */
    public void setUnifiedCode(String unifiedCode) {
        this.unifiedCode = unifiedCode;
    }

    /**
     * 登记地址
     */
    public String getLicAddress() {
        return this.licAddress;
    }

    /**
     * 登记地址
     */
    public void setLicAddress(String licAddress) {
        this.licAddress = licAddress;
    }

    /**
     * 省代码
     */
    public String getLicProvinceCode() {
        return this.licProvinceCode;
    }

    /**
     * 省代码
     */
    public void setLicProvinceCode(String licProvinceCode) {
        this.licProvinceCode = licProvinceCode;
    }

    /**
     * 市代码
     */
    public String getLicCityCode() {
        return this.licCityCode;
    }

    /**
     * 市代码
     */
    public void setLicCityCode(String licCityCode) {
        this.licCityCode = licCityCode;
    }

    /**
     * 区代码
     */
    public String getLicDistrictCode() {
        return this.licDistrictCode;
    }

    /**
     * 区代码
     */
    public void setLicDistrictCode(String licDistrictCode) {
        this.licDistrictCode = licDistrictCode;
    }

    /**
     * 营业执照有效期开始
     */
    public LocalDate getLicStart() {
        return this.licStart;
    }

    /**
     * 营业执照有效期开始
     */
    public void setLicStart(LocalDate licStart) {
        this.licStart = licStart;
    }

    /**
     * 营业执照有效期结束
     */
    public LocalDate getLicEnd() {
        return this.licEnd;
    }

    /**
     * 营业执照有效期结束
     */
    public void setLicEnd(LocalDate licEnd) {
        this.licEnd = licEnd;
    }

    /**
     * 实际营业地址
     */
    public String getRealAddress() {
        return this.realAddress;
    }

    /**
     * 实际营业地址
     */
    public void setRealAddress(String realAddress) {
        this.realAddress = realAddress;
    }

    /**
     * 实际省代码
     */
    public String getRealProvinceCode() {
        return this.realProvinceCode;
    }

    /**
     * 实际省代码
     */
    public void setRealProvinceCode(String realProvinceCode) {
        this.realProvinceCode = realProvinceCode;
    }

    /**
     * 实际市代码
     */
    public String getRealCityCode() {
        return this.realCityCode;
    }

    /**
     * 实际市代码
     */
    public void setRealCityCode(String realCityCode) {
        this.realCityCode = realCityCode;
    }

    /**
     * 实际区代码
     */
    public String getRealDistrictCode() {
        return this.realDistrictCode;
    }

    /**
     * 实际区代码
     */
    public void setRealDistrictCode(String realDistrictCode) {
        this.realDistrictCode = realDistrictCode;
    }

    /**
     * 法人姓名
     */
    public String getLegalName() {
        return this.legalName;
    }

    /**
     * 法人姓名
     */
    public void setLegalName(String legalName) {
        this.legalName = legalName;
    }

    /**
     * 法人手机
     */
    public String getLegalMobile() {
        return this.legalMobile;
    }

    /**
     * 法人手机
     */
    public void setLegalMobile(String legalMobile) {
        this.legalMobile = legalMobile;
    }

    /**
     * 联系人姓名
     */
    public String getLinkmanName() {
        return this.linkmanName;
    }

    /**
     * 联系人姓名
     */
    public void setLinkmanName(String linkmanName) {
        this.linkmanName = linkmanName;
    }

    /**
     * 联系人手机
     */
    public String getLinkmanMobile() {
        return this.linkmanMobile;
    }

    /**
     * 联系人手机
     */
    public void setLinkmanMobile(String linkmanMobile) {
        this.linkmanMobile = linkmanMobile;
    }


    @Override
    public String prefix() {
        return "SH";
    }
}
