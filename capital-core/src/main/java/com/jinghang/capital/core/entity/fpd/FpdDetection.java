package com.jinghang.capital.core.entity.fpd;


import com.jinghang.capital.core.entity.BaseEntity;
import com.jinghang.capital.core.enums.BankChannel;
import jakarta.persistence.Entity;
import jakarta.persistence.EnumType;
import jakarta.persistence.Enumerated;
import jakarta.persistence.Table;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * fpd检测表;
 *
 * <AUTHOR> http://www.chiner.pro
 * @date : 2025-3-29
 */
@Entity
@Table(name = "fpd_detection")
public class FpdDetection extends BaseEntity {

    /**
     * 资方渠道
     */
    @Enumerated(EnumType.STRING)
    private BankChannel channel;
    /**
     * 检测时间
     */
    private LocalDateTime detectionTime;
    /**
     * 检测天数（几天）
     */
    private Integer fpdDay;
    /**
     * 检测的账单日
     */
    private LocalDate repayDate;
    /**
     * 检测放款总金额（分母）
     */
    private BigDecimal detectionTotalAmt;
    /**
     * 首期逾期放款总金额（分子）
     */
    private BigDecimal fpdTotalAmt;
    /**
     * 总金额逾期率
     */
    private BigDecimal fpdRate;
    /**
     * 预警值
     */
    private BigDecimal fpdWarning;
    /**
     * 是否需要处理
     */
    private String needTreatment;
    /**
     * 检查时间
     */
    private LocalDateTime checkTime;
    /**
     * 检查首期逾期放款总金额（分子）
     */
    private BigDecimal checkFpdTotalAmt;
    /**
     * 检查总金额逾期率
     */
    private BigDecimal checkFpdRate;

    public BankChannel getChannel() {
        return channel;
    }

    public void setChannel(BankChannel channel) {
        this.channel = channel;
    }

    public LocalDateTime getDetectionTime() {
        return detectionTime;
    }

    public void setDetectionTime(LocalDateTime detectionTime) {
        this.detectionTime = detectionTime;
    }

    public Integer getFpdDay() {
        return fpdDay;
    }

    public void setFpdDay(Integer fpdDay) {
        this.fpdDay = fpdDay;
    }

    public LocalDate getRepayDate() {
        return repayDate;
    }

    public void setRepayDate(LocalDate repayDate) {
        this.repayDate = repayDate;
    }

    public BigDecimal getDetectionTotalAmt() {
        return detectionTotalAmt;
    }

    public void setDetectionTotalAmt(BigDecimal detectionTotalAmt) {
        this.detectionTotalAmt = detectionTotalAmt;
    }

    public BigDecimal getFpdTotalAmt() {
        return fpdTotalAmt;
    }

    public void setFpdTotalAmt(BigDecimal fpdTotalAmt) {
        this.fpdTotalAmt = fpdTotalAmt;
    }

    public BigDecimal getFpdRate() {
        return fpdRate;
    }

    public void setFpdRate(BigDecimal fpdRate) {
        this.fpdRate = fpdRate;
    }

    public BigDecimal getFpdWarning() {
        return fpdWarning;
    }

    public void setFpdWarning(BigDecimal fpdWarning) {
        this.fpdWarning = fpdWarning;
    }

    public String getNeedTreatment() {
        return needTreatment;
    }

    public void setNeedTreatment(String needTreatment) {
        this.needTreatment = needTreatment;
    }

    public LocalDateTime getCheckTime() {
        return checkTime;
    }

    public void setCheckTime(LocalDateTime checkTime) {
        this.checkTime = checkTime;
    }

    public BigDecimal getCheckFpdTotalAmt() {
        return checkFpdTotalAmt;
    }

    public void setCheckFpdTotalAmt(BigDecimal checkFpdTotalAmt) {
        this.checkFpdTotalAmt = checkFpdTotalAmt;
    }

    public BigDecimal getCheckFpdRate() {
        return checkFpdRate;
    }

    public void setCheckFpdRate(BigDecimal checkFpdRate) {
        this.checkFpdRate = checkFpdRate;
    }
}
