package com.jinghang.capital.core.enums;


import com.jinghang.capital.core.exception.BizErrorCode;
import com.jinghang.capital.core.exception.BizException;

import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

public enum AgreementType {


    // 长银直连 协议
    // 综合授权书
    SYNTHESIS_AUTHORIZATION_CYBK(FileType.SYNTHESIS_AUTHORIZATION, ".pdf",
            "00005", SignatureType.TEMPLATE, LoanStage.CREDIT, BankChannel.CYBK,
            false),

    /**
     * 放款成功后
     */
    // 数字证书使用授权协议
    DIGITAL_CERTIFICATE_AUTHORIZATION_LETTER_CYBK(FileType.DIGITAL_CERTIFICATE_AUTHORIZATION_LETTER, ".pdf",
            "00006", SignatureType.TEMPLATE, LoanStage.CREDIT, BankChannel.CYBK,
            false),
    // 扣款授权书
    ENTRUSTED_DEDUCTION_LETTER_CYBK(FileType.ENTRUSTED_DEDUCTION_LETTER, ".pdf",
            "00008", SignatureType.TEMPLATE, LoanStage.LOAN, BankChannel.CYBK,
            false),


    // 蚂蚁相关协议
    // 综合授权书
    SYNTHESIS_AUTHORIZATION_HXBK(FileType.SYNTHESIS_AUTHORIZATION, ".pdf",
            "00005", SignatureType.TEMPLATE, LoanStage.CREDIT, BankChannel.HXBK,
            false),

    /**
     * 放款成功后
     */
    // 数字证书使用授权协议
    DIGITAL_CERTIFICATE_AUTHORIZATION_LETTER_HXBK(FileType.DIGITAL_CERTIFICATE_AUTHORIZATION_LETTER, ".pdf",
            "00006", SignatureType.TEMPLATE, LoanStage.CREDIT, BankChannel.HXBK,
            false),
    // 扣款授权书
    ENTRUSTED_DEDUCTION_LETTER_HXBK(FileType.ENTRUSTED_DEDUCTION_LETTER, ".pdf",
            "00008", SignatureType.TEMPLATE, LoanStage.LOAN, BankChannel.HXBK,
            false);

    private final FileType fileType;

    private final String extension;

    private final String templateNo;

    private final SignatureType signatureType;

    private final LoanStage loanStage;

    /**
     * 资方渠道
     */
    private final BankChannel bankChannel;

    private final Boolean needSignAgain;


    AgreementType(FileType fileType, String extension, String templateNo, SignatureType signatureType,
                  LoanStage loanStage, BankChannel bankChannel, Boolean needSignAgain) {
        this.fileType = fileType;
        this.extension = extension;
        this.templateNo = templateNo;
        this.signatureType = signatureType;
        this.loanStage = loanStage;
        this.bankChannel = bankChannel;
        this.needSignAgain = needSignAgain;
    }


    public static List<AgreementType> getAgreements(LoanStage loanStage, BankChannel bankChannel) {
        return Arrays.stream(values())
            .filter(agreementType -> agreementType.bankChannel.equals(bankChannel) && agreementType.loanStage.equals(loanStage))
            .collect(Collectors.toList());
    }

    public static AgreementType getAgreements(LoanStage loanStage, BankChannel bankChannel, String templateNo) {
        return Arrays.stream(values())
            .filter(agreementType -> bankChannel == agreementType.bankChannel
                && agreementType.loanStage == loanStage
                && templateNo.equalsIgnoreCase(agreementType.getTemplateNo()))
            .findFirst().orElseThrow(() -> new BizException(BizErrorCode.CONTRACT_TEMPLATE_NOT_FOUND));
    }


    public static List<AgreementType> getAgreements(BankChannel bankChannel, Boolean signAgain) {
        return Arrays.stream(values())
            .filter(agreementType -> agreementType.bankChannel.equals(bankChannel) && agreementType.needSignAgain.equals(signAgain))
            .collect(Collectors.toList());
    }

    public static AgreementType getAgreement(BankChannel bankChannel, LoanStage loanStage, SignatureType signatureType, FileType fileType) {
        return Arrays.stream(values())
            .filter(agreementType -> agreementType.bankChannel.equals(bankChannel)
                && agreementType.signatureType.equals(signatureType)
                && agreementType.loanStage.equals(loanStage)
                && agreementType.fileType.equals(fileType)
            ).findFirst().orElse(null);
    }

    public static List<AgreementType> getAgreement(BankChannel bankChannel, LoanStage loanStage, SignatureType signatureType) {
        return Arrays.stream(values())
            .filter(agreementType -> agreementType.bankChannel.equals(bankChannel)
                && agreementType.signatureType.equals(signatureType)
                && agreementType.loanStage.equals(loanStage)
            ).toList();
    }

    public static AgreementType getAgreement(FileType fileType, BankChannel bankChannel) {
        return Arrays.stream(values())
            .filter(agreementType -> agreementType.bankChannel.equals(bankChannel) && agreementType.fileType.equals(fileType))
            .findFirst().orElse(null);
    }

    public static AgreementType getAgreement(LoanStage loanStage, SignatureType signatureType, FileType fileType) {
        return Arrays.stream(values())
            .filter(agreementType -> agreementType.signatureType.equals(signatureType)
                && agreementType.loanStage.equals(loanStage)
                && agreementType.fileType.equals(fileType)
            ).findFirst().orElse(null);
    }


    public FileType getFileType() {
        return fileType;
    }

    public String getExtension() {
        return extension;
    }

    public String getTemplateNo() {
        return templateNo;
    }

    public SignatureType getSignatureType() {
        return signatureType;
    }

    public LoanStage getLoanStage() {
        return loanStage;
    }

    public BankChannel getBankChannel() {
        return bankChannel;
    }

    public Boolean getNeedSignAgain() {
        return needSignAgain;
    }
}
