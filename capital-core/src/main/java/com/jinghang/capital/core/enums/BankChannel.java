package com.jinghang.capital.core.enums;


import com.jinghang.capital.core.exception.BizErrorCode;
import com.jinghang.capital.core.exception.BizException;

import java.math.BigDecimal;

/**
 * 资方渠道
 */
public enum BankChannel {

    /**
     * 长银银行 直连
     */
    CYBK("CYBK", "长银直连", new BigDecimal("0.2399")),
    /**
     * 蚂蚁 - 湖消 （对接蚂蚁）
     */
    HXBK("HXBK", "湖消 间接连接，通过对接蚂蚁放款", new BigDecimal("0.2399")),
    /**
     * 润楼苏商
     */
    RL_SUS("RL_SUSHANG", "润楼苏商", new BigDecimal("0.24"))

    ;


    private final String code;

    private final String desc;

    private final BigDecimal irrRate;

    BankChannel(String code, String desc, BigDecimal irrRate) {
        this.code = code;
        this.desc = desc;
        this.irrRate = irrRate;
    }

    public String getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }

    public BigDecimal getIrrRate() {
        return irrRate;
    }

    public static BankChannel findByCode(String code) {
        BankChannel[] values = BankChannel.values();
        for (BankChannel ch : values) {
            if (ch.getCode().equals(code)) {
                return ch;
            }
        }
        throw new BizException(BizErrorCode.CHANNEL_NOT_FOUND);
    }
}
