package com.jinghang.capital.core.enums;



public enum FileType {
    ID_HEAD("人头面"),
    ID_NATION("国徽面"),
    ID_FACE("人脸"),
    ENTRUSTED_DEDUCTION_LETTER("委托扣款授权书"),
    ENTRUSTED_DEDUCTION_LETTER_YTX("委托扣款授权书-益通祥"),
    CREDIT_APPLY("授信申请书"),
    LOAN_CONTRACT("借款合同"),
    PERSONAL_CREDIT_AUTHORIZATION_LETTER("个人征信授权书"),
    PERSONAL_CREDIT_AUTHORIZATION_LETTER_LHD("个人征信授权书(联合贷)"),
    PERSONAL_INFORMATION_LETTER("个人信息授权书(资方)"),
    PERSONAL_INFORMATION_QUERY_APPLY_LETTER("个人信息查询及使用授权书(资方)"),
    PERSONAL_INFORMATION_QUERY_SUBMIT_LETTER("个人信息查询及报送授权书(资方)"),
    PERSONAL_INFORMATION_QUERY_LETTER("个人信息查询及使用授权书"),
    PERSONAL_INFORMATION_QUERY_LETTER_LHD("个人信息查询及使用授权书(联合贷)"),
    PERSONAL_INFORMATION_QUERY_LETTER_TEC("个人信息查询及使用授权书(科技公司)"),
    PERSONAL_INFORMATION_QUERY_LETTER_QJYLF3("个人信息查询及使用授权书(亲家亿联F003)"),
    PERSONAL_INFORMATION_QUERY_LETTER_LSB("个人信息查询及使用授权书(临商银行)"),
    PERSONAL_INFORMATION_QUERY_LETTER_HRB("个人信息查询及使用授权书(华润银行)"),
    PERSONAL_INFORMATION_QUERY_LETTER_YTX("个人信息查询及使用授权书(益通祥)"),
    PERSONAL_INFORMATION_QUERY_LETTER_QH("个人信息查询及使用授权书(轻花)"),
    SENSITIVE_PERSONAL_INFORMATION_HANDLE_LETTER("敏感个人信息处理授权书"),
    BORROWER_IMPORTANT_INFORMATION_TIPS("借款人重要信息提示"),
    GUARANTEE_CONSULT_CONTRACT_YTX("担保咨询服务合同(益通祥)"),

    PERSONAL_LOAN_USE_COMMITMENT("个人贷款用途承诺书"),
    CONSUMER_CREDIT_SERVICE_USER_AGREEMENT("消费信贷服务用户使用协议(海尔消金)"),
    DEBT_CONFIRMATION_AGREEMENT("债权确认协议"),
    PERSONAL_LOAN_CUSTOMER_COMMIT_CONFIRMATION("个人贷款客户承诺确认书"),
    SYNTHESIS_AUTHORIZATION("综合授权书"),
    DIGITAL_CERTIFICATE_AUTHORIZATION_LETTER("数字证书授权使用书"),

    JUNXIN_RISK_SHARE_CONFIRM("钧信_风险分担确认函"),

    JUNXIN_PERSONAL_CREDIT_AUTHORIZATION_LETTER("钧信_个人征信授权书"),

    DELEGATE_SWIFT_AUTHORIZATION_LETTER("委托快捷签署授权书"),
    PERSONAL_ACCOUNT_AUTHORIZATION_LETTER("个人账户授权书"),
    ENTRUSTED_GUARANTEE_CONTRACT("委托担保合同"),
    IRREVOCABLE_GUARANTEE_LETTER("不可撤销担保函"),
    GUARANTEE_CREDIT_ASSESS_CONTRACT("担保合同-信用评估"),
    PROMISE_NOT_STUDENT("非学生承诺函"),
    AMOUNT_CONTRACT("额度合同"),

    LOAN_FILE("放款对账文件"),
    REPAYMENT_FILE("还款对账文件"),
    REPAY_PLAN_FILE("还款计划对账文件"),
    COMPENSATION_INNER_MARK_FILE("代偿&回购标记文件"),
    COMPENSATION_FILE("代偿文件"),
    REPURCHASE_FILE("回购文件"),
    DUE_FILE("还款计划担保费文件"),
    DUE_ON_LOAN_FILE("在贷还款计划明细文件"),
    LOAN_VOUCHER_FILE("放款凭证文件"),
    COMPENSATORY_VOUCHER_FILE("代偿凭证文件"),
    CREDIT_SETTLE_VOUCHER_FILE("结清证明文件"),

    DISCOUNT_FILE("营销减免文件"),
    @Deprecated
    LOAN_ISSUANCE_CERTIFICATE("贷款发放凭证"),
    PENALTY_FILE("罚息文件"),
    RECOVERY_FILE("追偿还款文件"),
    OFFLINE_REPAY_FILE("线下代偿前还款文件"),
    GEOGRAPHICAL_COMMITMENT("地域承诺函"),
    AFTER_LOAN_FILE("贷后明细数据文件"),
    CLAIM_AFTER_LOAN_FILE("代偿后放款文件"),
    CLAIM_AFTER_REPAY_PLAN_FILE("代偿后还款计划文件"),
    REPAY_RECORD_FILE("还款记录数据文件"),
    REPAY_RECORD_DETAIL_FILE("还款明细记录数据文件"),
    PERSONAL_CREDIT_REPORT_INQUIRY_AUTHORIZATION("个人信用报告查询授权书"),
    COLLECTION_AGREEMENT("代收付协议"),
    PERSONAL_INFO_QUERY_SIGN_AUTH_LETTER("个人信息查询及签章授权协议"),

    FUND_ADD_INSTRUCT("资金追加指令文件"),
    FUND_TRANSFER_INSTRUCT("信托划款指令文件"),
    SYNTHESIS_CREDIT_AUTHORIZATION_LETTER_UNION("联合行综合征信授权书"),
    PERSONAL_LOAN_CREDIT_AGREEMENT("个人贷款授信协议"),

    // 添加征信授权书
    PERSONAL_CREDIT_AUTHORIZATION_LETTER_CREDIT("征信授权书"),

    // 添加仲裁协议
    ARBITRATION_AGREEMENT("仲裁协议"),
    ;

    private String desc;

    FileType(String desc) {
        this.desc = desc;
    }

    public String getDesc() {
        return desc;
    }
}
