package com.jinghang.capital.core.enums;

import java.util.Arrays;

/**
 * <AUTHOR>
 * @date 2023/5/14
 */
public enum Industry {
    /*建筑业*/
    ONE(1, "建筑业"),
    /*电力、热力、燃气及水生产和供应业*/
    TWO(2, "电力、热力、燃气及水生产和供应业"),
    /*交通运输、仓储和邮政业*/
    THREE(3, "交通运输、仓储和邮政业"),
    /*教育*/
    FOUR(4, "教育"),
    /*房地产业*/
    FIVE(5, "房地产业"),
    /*住宿和餐饮业*/
    SIX(6, "住宿和餐饮业"),
    /*批发和零售业*/
    SEVEN(7, "批发和零售业"),
    /*租赁和商务服务业*/
    EIGHT(8, "租赁和商务服务业"),
    /*信息传输、软件和信息技术服务业*/
    NINE(9, "信息传输、软件和信息技术服务业"),
    /*金融业*/
    TEN(10, "金融业"),
    /*文化、体育和娱乐业*/
    ELEVEN(11, "文化、体育和娱乐业"),
    /*制造业*/
    TWELVE(12, "制造业"),
    /*水利、环境和公共设施管理业*/
    THIRTEEN(13, "水利、环境和公共设施管理业"),
    /*采矿业*/
    FOURTEEN(14, "采矿业"),
    /*农、林、牧、渔业*/
    FIFTEEN(15, "农、林、牧、渔业"),
    /*居民服务、修理和其他服务业*/
    SIXTEEN(16, "居民服务、修理和其他服务业"),
    /*卫生和社会工作*/
    SEVENTEEN(17, "卫生和社会工作"),
    /*科学研究和技术服务业*/
    EIGHTEEN(18, "科学研究和技术服务业"),
    /*公共管理、社会保障和社会组织*/
    NINETEEN(19, "公共管理、社会保障和社会组织"),
    /*其他*/
    TWENTY(20, "其他");

    private Integer code;

    private String desc;

    Industry(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public static Industry getIndustry(Integer code) {
        return Arrays.stream(Industry.values()).filter(industry -> industry.getCode().equals(code)).findFirst().orElse(Industry.TWENTY);
    }


    public static Industry getIndustryByName(String name) {
        return Arrays.stream(Industry.values())
            .filter(industry -> industry.name().equals(name))
            .findFirst().orElse(Industry.TWENTY);
    }




    public Integer getCode() {
        return code;
    }

    public void setCode(Integer code) {
        this.code = code;
    }

    public String getDesc() {
        return desc;
    }

    public void setDesc(String desc) {
        this.desc = desc;
    }
}
