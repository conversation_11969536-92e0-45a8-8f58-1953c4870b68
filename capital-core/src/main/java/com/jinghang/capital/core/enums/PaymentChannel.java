package com.jinghang.capital.core.enums;


import com.jinghang.capital.api.dto.ProtocolChannel;

import java.util.Arrays;

/**
 *
 *
 * 支付通道 参考entrance
 */
public enum PaymentChannel {
    BAOFU("宝付", ProtocolChannel.BF),
    ALLINPAY("通联", ProtocolChannel.ALLIN_PAY),
    YEEPAY("易宝", ProtocolChannel.YEE_PAY);

    private String desc;
    private ProtocolChannel protocolChannel;

    PaymentChannel(String desc, ProtocolChannel protocolChannel) {
        this.desc = desc;
        this.protocolChannel = protocolChannel;
    }


    public String getDesc() {
        return desc;
    }

    public ProtocolChannel getProtocolChannel() {
        return protocolChannel;
    }

    public static PaymentChannel getEnumByName(String name) {
        return Arrays.stream(values()).filter(p -> name.equalsIgnoreCase(p.name())).findFirst().orElseThrow();
    }
}
