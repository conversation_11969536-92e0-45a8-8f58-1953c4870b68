package com.jinghang.capital.core.enums;

import java.util.Arrays;

/**
 * <AUTHOR>
 * @date 2023/5/14
 */
public enum Position {
    /*其他*/
    ELEVEN(11, "其他"),
    /*一线生产、建筑、运输、配送人员*/
    THIRTEEN(13, "一线生产、建筑、运输、配送人员"),
    /*学校、培训机构等教育人员*/
    FOURTEEN(14, "学校、培训机构等教育人员"),
    /*一般销售人员，如保险、房产、汽车等*/
    FIFTEEN(15, "一般销售人员，如保险、房产、汽车等"),
    /*个体经营及企业法人*/
    SIXTEEN(16, "个体经营及企业法人"),
    /*物业、保安、保洁人员*/
    SEVENTEEN(17, "物业、保安、保洁人员"),
    /*一般服务人员，如KTV、酒吧、餐饮、网咖等*/
    EIGHTEEN(18, "一般服务人员，如KTV、酒吧、餐饮、网咖等"),
    /*国家机关、国企央企、事业单位人员*/
    NINETEEN(19, "国家机关、国企央企、事业单位人员"),
    /*医生、医护等医疗人员*/
    TWENTY(20, "医生、医护等医疗人员"),
    /*专业技术人员*/
    TWENTY_ONE(21, "专业技术人员"),

    /*军人*/
    TWENTY_TWO(22, "军人"),
    TWENTY_FOUR(24, "农、林、牧、渔、水利业生产人员");

    private Integer code;

    private String desc;

    Position(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }


    public static Position getByName(String name) {
        return Arrays.stream(Position.values())
            .filter(pos -> pos.name().equals(name))
            .findFirst().orElse(Position.ELEVEN);
    }



    public Integer getCode() {
        return code;
    }

    public void setCode(Integer code) {
        this.code = code;
    }

    public String getDesc() {
        return desc;
    }

    public void setDesc(String desc) {
        this.desc = desc;
    }
}
