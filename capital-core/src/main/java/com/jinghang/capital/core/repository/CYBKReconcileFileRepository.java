package com.jinghang.capital.core.repository;

import com.jinghang.capital.core.entity.CYBKReconcileFile;
import org.springframework.data.jpa.repository.JpaRepository;

import java.time.LocalDate;
import java.util.List;

public interface CYBKReconcileFileRepository extends JpaRepository<CYBKReconcileFile, String> {

    List<CYBKReconcileFile> findByChannelAndFileDateAndReccType(String channel, LocalDate fileDate, String reccFileType);

    List<CYBKReconcileFile> findByProductAndChannelAndFileDate(String product, String channel, LocalDate fileDate);

}
