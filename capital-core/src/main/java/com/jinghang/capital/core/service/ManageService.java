package com.jinghang.capital.core.service;


import com.alibaba.fastjson.JSON;
import com.jinghang.capital.api.dto.ClaimResult;
import com.jinghang.capital.api.dto.QuotaStage;
import com.jinghang.capital.api.dto.repay.PartRepayType;
import com.jinghang.capital.api.dto.repay.RepayReturnUploadResultDto;
import com.jinghang.capital.core.banks.BankBindService;
import com.jinghang.capital.core.banks.BankCallbackService;
import com.jinghang.capital.core.banks.BankContractInfoService;
import com.jinghang.capital.core.banks.BankCreditService;
import com.jinghang.capital.core.banks.BankFileService;
import com.jinghang.capital.core.banks.BankLoanService;
import com.jinghang.capital.core.banks.BankPlanService;
import com.jinghang.capital.core.banks.BankProtocolSyncService;
import com.jinghang.capital.core.banks.BankQuotaService;
import com.jinghang.capital.core.banks.BankReccService;
import com.jinghang.capital.core.banks.BankRepayService;
import com.jinghang.capital.core.convert.entity.RepayConvert;
import com.jinghang.capital.core.convert.entityvo.VoCreditConvert;
import com.jinghang.capital.core.dto.*;
import com.jinghang.capital.core.entity.*;
import com.jinghang.capital.core.enums.AgreementType;
import com.jinghang.capital.core.enums.BankChannel;
import com.jinghang.capital.core.enums.FileType;
import com.jinghang.capital.core.enums.LoanStage;
import com.jinghang.capital.core.enums.ProcessStatus;
import com.jinghang.capital.core.enums.RepayStatus;
import com.jinghang.capital.core.enums.RepayType;
import com.jinghang.capital.core.enums.SyncState;
import com.jinghang.capital.core.exception.BizErrorCode;
import com.jinghang.capital.core.exception.BizException;
import com.jinghang.capital.core.repository.*;
import com.jinghang.capital.core.service.remote.nfsp.sign.req.SignApplyReq;
import com.jinghang.capital.core.util.AmountUtil;
import com.jinghang.capital.core.vo.BusinessChronosProcessResultVo;
import com.jinghang.capital.core.vo.BusinessChronosProcessVo;
import com.jinghang.capital.core.vo.bind.BindApplyVo;
import com.jinghang.capital.core.vo.bind.BindConfirmVo;
import com.jinghang.capital.core.vo.bind.BindResultVo;
import com.jinghang.capital.core.vo.credit.CreditApplyVo;
import com.jinghang.capital.core.vo.credit.CreditQueryVo;
import com.jinghang.capital.core.vo.credit.CreditResultVo;
import com.jinghang.capital.core.vo.credit.ExtInfoVo;
import com.jinghang.capital.core.vo.credit.PreCreditApplyResultVo;
import com.jinghang.capital.core.vo.credit.PreCreditApplyVo;
import com.jinghang.capital.core.vo.credit.RecreditApplyVo;
import com.jinghang.capital.core.vo.file.FileDailyProcessVo;
import com.jinghang.capital.core.vo.file.FileDownloadRequestVo;
import com.jinghang.capital.core.vo.file.FileDownloadResultVo;
import com.jinghang.capital.core.vo.file.FileDownloadVo;
import com.jinghang.capital.core.vo.file.FilePushVo;
import com.jinghang.capital.core.vo.file.FileSynRepayPlanResultDVo;
import com.jinghang.capital.core.vo.file.FileSynRepayPlanVo;
import com.jinghang.capital.core.vo.file.FileSyncDueFileResultVo;
import com.jinghang.capital.core.vo.file.FileSyncDueFileVo;
import com.jinghang.capital.core.vo.file.FileUploadResultVo;
import com.jinghang.capital.core.vo.file.FileUploadVo;
import com.jinghang.capital.core.vo.file.PreviewResultVo;
import com.jinghang.capital.core.vo.file.PreviewVo;
import com.jinghang.capital.core.vo.loan.LoanApplyVo;
import com.jinghang.capital.core.vo.loan.LoanLprQueryVo;
import com.jinghang.capital.core.vo.loan.LoanLprResultVo;
import com.jinghang.capital.core.vo.loan.LoanNoticeResultVo;
import com.jinghang.capital.core.vo.loan.LoanNoticeVo;
import com.jinghang.capital.core.vo.loan.LoanQueryVo;
import com.jinghang.capital.core.vo.loan.LoanResultVo;
import com.jinghang.capital.core.vo.loan.LoanTrialQueryVo;
import com.jinghang.capital.core.vo.loan.LoanTrialResultVo;
import com.jinghang.capital.core.vo.loan.OrderCancelResultVo;
import com.jinghang.capital.core.vo.loan.OrderCancelVo;
import com.jinghang.capital.core.vo.protocol.ProtocolSyncVo;
import com.jinghang.capital.core.vo.recc.*;
import com.jinghang.capital.core.vo.repay.*;


import com.jinghang.common.util.CollectionUtil;
import com.jinghang.common.util.StringUtil;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.temporal.ChronoUnit;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.function.Function;
import java.util.stream.Collectors;

@Service
public class ManageService {
    private static final Logger logger = LoggerFactory.getLogger(ManageService.class);

    public static final int SLEEP_MILLES = 100;

    private List<BankFileService> bankFileServices;
    private List<BankCreditService> bankCreditServices;
    private List<BankProtocolSyncService> bankProtocolSyncServices;
    private List<BankLoanService> bankLoanServices;
    private List<BankPlanService> bankPlanServices;
    private List<BankRepayService> bankRepayServices;

    @Autowired
    private List<BankReccService> bankReccServices;
    @Autowired
    private CreditExtRepository creditExtRepository;
    @Autowired
    private List<BankBindService> bankBindServices;

    @Autowired
    private List<BankContractInfoService> bankContractInfoServices;

    @Autowired
    private List<BankQuotaService> bankQuotaServices;

    @Autowired
    private List<BankCallbackService> bankCallbackServices;

    @Autowired
    private CustomerRepayRecordRepository customerRepayRecordRepository;

    @Autowired
    private WarningService warningService;

    private CommonService commonService;
    @Autowired
    private CovenantDownloadApplyRecordRepository covenantDownloadApplyRecordRepository;
    @Autowired
    private BankRepayRecordRepository bankRepayRecordRepository;


    @Autowired
    public ManageService(List<BankFileService> bankFileServices,
                         List<BankCreditService> bankCreditServices,
                         List<BankProtocolSyncService> bankProtocolSyncServices, List<BankLoanService> bankLoanServices,
                         List<BankPlanService> bankPlanServices, List<BankRepayService> bankRepayServices, CommonService commonService) {
        this.bankFileServices = bankFileServices;
        this.bankCreditServices = bankCreditServices;
        this.bankProtocolSyncServices = bankProtocolSyncServices;
        this.bankLoanServices = bankLoanServices;
        this.bankPlanServices = bankPlanServices;
        this.bankRepayServices = bankRepayServices;
        this.commonService = commonService;
    }

    /**
     * 去资方申请授信
     *
     * @param creditId
     */
    public void creditApply(String creditId) {
        Credit credit = commonService.findCreditById(creditId);
        getBankCreditService(credit.getChannel()).bankApply(credit);
    }

    /**
     * 本地处理资产端过来的授信申请
     *
     * @param creditApply
     * @return
     */
    public CreditResultVo creditApply(CreditApplyVo<ExtInfoVo> creditApply) {
        BankChannel bankChannel = creditApply.getBankChannel();
        return getBankCreditService(bankChannel).apply(creditApply);
    }

    /**
     * 查库返回授信结果给外部
     *
     * @param creditQuery
     * @return
     */
    public CreditResultVo creditQuery(CreditQueryVo creditQuery) {
        Credit credit = StringUtil.isNotBlank(creditQuery.getCreditId())
            ? commonService.findCreditById(creditQuery.getCreditId()) : commonService.findCreditByOutId(creditQuery.getSysId());

        CreditResultVo resultVo = VoCreditConvert.INSTANCE.toCreditResultDto(credit);
        creditExtRepository.findById(credit.getId()).ifPresent(ext -> {
            resultVo.setCreditResultAmt(ext.getAvailableCreditAmt());
        });

        return resultVo;
    }

    /**
     * 去资方查询授信结果（从消息监听触发）
     *
     * @param creditId 授信id
     * @return
     */
    public void creditQuery(String creditId) {
        Credit credit = commonService.findCreditById(creditId);
        getBankCreditService(credit.getChannel()).query(new CreditQueryVo(creditId));
    }


    /**
     * 本地处理资产端风控失败，通知过来的授信申请.
     * creditController 入口
     *
     * @param creditApply
     * @return
     */
    public CreditResultVo creditRiskFailedNotify(CreditApplyVo<ExtInfoVo> creditApply) {
        BankChannel bankChannel = creditApply.getBankChannel();
        return getBankCreditService(bankChannel).failedNotify(creditApply);
    }

    /**
     * 去资方申请授信
     *
     * @param failedCreditId
     */
    public void creditFailedNotify(String failedCreditId) {
        FailedCredit credit = commonService.findFailedCreditById(failedCreditId);
        getBankCreditService(credit.getChannel()).bankFailedNotify(credit);
    }


    public void protocolSync(ProtocolSyncVo protocolSyncVo) {
        Credit credit = commonService.findCreditById(protocolSyncVo.getCreditId());
        getBankProtocolService(credit.getChannel()).protocolSync(protocolSyncVo);
    }

    /**
     * 资方放款申请, 监听入口
     *
     * @param loanId 借据id
     */
    public void loanApply(String loanId) {
        Loan loan = commonService.findLoanById(loanId);
        getBankLoanService(loan.getChannel()).bankApply(loan);
    }





    public LoanResultVo loanApply(LoanApplyVo loanApply) {
        String creditId = loanApply.getCreditId();
        // 查授信订单
        Credit credit = StringUtil.isNotBlank(creditId) ? commonService.findCreditById(creditId) : commonService.findCreditByOutId(loanApply.getSysCreditId());
        // 放款申请
        LoanResultVo result = getBankLoanService(credit.getChannel()).apply(loanApply);

        return result;
    }

    public LoanResultVo loanQuery(LoanQueryVo loanQuery) {
        String loanId = loanQuery.getLoanId();
        Loan loan = getLoanByIds(loanId, loanQuery.getSysId());
        if (!loan.getOuterLoanId().equals(loanQuery.getSysId())) {
            throw new BizException(BizErrorCode.LOAN_ID_INVALID);
        }
        return getBankLoanService(loan.getChannel()).loanQuery(loan);
    }

    /**
     * 内部队列查询
     *
     * @param loanId
     */
    public void loanQuery(String loanId) {
        Loan loan = commonService.findLoanById(loanId);
        getBankLoanService(loan.getChannel()).query(new LoanQueryVo(loanId));
    }

    /**
     * 同步还款计划给core，落库
     *
     * @param planSyncVo 还款计划同步
     */
    public void planSync(PlanSyncVo planSyncVo) {
        // 同步还款计划给资方
        getBankPlanService(planSyncVo.getBankChannel()).planSync(planSyncVo);
    }

    /**
     * 借据id
     *
     * @param loanId
     */
    public void syncPlanToCapital(String loanId) {
        Loan loan = getCommonService().findLoanById(loanId);
        getBankPlanService(loan.getChannel()).syncPlanToCapital(loanId);
    }

    public void syncReplanManual(BankProcessDTO dto) {
        if (dto.getChannel() == null) {
            return;
        }
        getBankPlanService(dto.getChannel()).syncRePlanManual(dto);
    }

    public void planPush(String recFileId) {
        // 同步文件给core, 只有wld实现
        ReconciliationFile recFile = commonService.findRecFileById(recFileId);
        getBankPlanService(recFile.getBankChannel()).planPush(recFileId);
        // 推送状态更新
        recFile.setSyncState(SyncState.Y);
        commonService.updateRecFile(recFile);
    }

    public PlanVo queryRepayPlan(PlanQueryVo planQueryVo) {
        Loan loan = getLoanByIds(planQueryVo.getLoanId(), planQueryVo.getSysLoanId());
        return getBankRepayService(loan.getChannel()).queryRepayPlan(planQueryVo);
    }

    public OverduePlanVo queryOverduePlan(OverduePlanQueryVo planQueryVo) {
        Loan loan = getLoanByIds(planQueryVo.getLoanId(), planQueryVo.getSysLoanId());
        return getBankRepayService(loan.getChannel()).queryOverduePlan(planQueryVo);
    }

    private Loan getLoanByIds(String loanId, String outerLoanId) {
        return StringUtil.isNotBlank(loanId) ? commonService.findLoanById(loanId) : commonService.findLoanByOutId(outerLoanId);
    }

    private Loan getLoanById(String loanId) {
        return commonService.findLoanById(loanId);
    }

    private LoanReplan findByLoanIdAndPeriod(String loanId, Integer period) {
        return commonService.findByLoanIdAndPeriod(loanId, period);
    }

    public void contractDownload(String businessId, LoanStage stage) {
        switch (stage) {
            case CREDIT -> {
                Credit credit = commonService.findCreditById(businessId);
                getBankCreditService(credit.getChannel()).contractDownload(businessId);
            }
            case LOAN -> {
                Loan loan = commonService.findLoanById(businessId);
                getBankLoanService(loan.getChannel()).contractDownload(businessId);
            }
            default -> {
            }
        }
    }


    public void contractUpload(String businessId, LoanStage stage) {
        switch (stage) {
            case CREDIT -> {
                Credit credit = commonService.findCreditById(businessId);
                getBankCreditService(credit.getChannel()).contractUpload(businessId);
            }
            case LOAN -> {
                Loan loan = commonService.findLoanById(businessId);
                getBankLoanService(loan.getChannel()).contractUpload(businessId);
            }
            default -> {
            }
        }
    }

    public void bankSignQuery(String businessId, LoanStage stage) {
        switch (stage) {
            case CREDIT -> {
                Credit credit = commonService.findCreditById(businessId);
                getBankCreditService(credit.getChannel()).bankSignQuery(businessId);
            }
            case LOAN -> {
                Loan loan = commonService.findLoanById(businessId);
                getBankLoanService(loan.getChannel()).bankSignQuery(businessId);
            }
            default -> {
            }
        }
    }

    public void adjustLimit(AdjustLimitBizDto bizDto) {
        BankChannel bankChannel = bizDto.getBankChannel();
        getBankCreditService(bankChannel).adjustLimitQuery(bizDto);
    }


    public FileDownloadResultVo fileDownload(FileDownloadVo downloadVo) {
        BankChannel bankChannel = downloadVo.getBankChannel();
        FileType fileType = downloadVo.getType();
        if (bankChannel == null
            && (fileType == FileType.REPAYMENT_FILE
            || fileType == FileType.LOAN_FILE
            || fileType == FileType.PENALTY_FILE
            || fileType == FileType.COMPENSATION_FILE)) {
            // 新逻辑按产品出内部对账文件,不区分渠道
            return reccFileDownload(downloadVo);
        }

        return getBankFileService(bankChannel).download(downloadVo);
    }

    public FileDownloadResultVo voucherFileDownload(FileDownloadVo downloadVo) throws BizException {
        return getBankFileService(downloadVo.getBankChannel()).download(downloadVo);
    }

    private FileDownloadResultVo reccFileDownload(FileDownloadVo downloadVo) {
        LocalDate fileDate = downloadVo.getFileDate();
        ReconciliationFile reccFile =
            commonService.getRecFileRepository().findFirstByFileTypeAndProductAndFileDateOrderByCreatedTimeDesc(downloadVo.getType(),
                downloadVo.getProduct(), fileDate);

        FileDownloadResultVo resultVo = new FileDownloadResultVo();
        if (reccFile == null) {
            return resultVo;
        }

        resultVo.setOssBucket(reccFile.getTargetOssBucket());
        resultVo.setOssPath(reccFile.getTargetOssKey());
        resultVo.setFileName(reccFile.getFileName());
        return resultVo;
    }

    public FileUploadResultVo fileUpload(FileUploadVo uploadVo) {
        BankChannel bankChannel = uploadVo.getBankChannel();
        return getBankFileService(bankChannel).upload(uploadVo);
    }

    public PreviewResultVo filePreview(PreviewVo previewVo) {
        BankChannel bankChannel = previewVo.getBankChannel();
        return getBankFileService(bankChannel).preview(previewVo);
    }

    public void filePush(FilePushVo pushVo) {
        BankChannel bankChannel = pushVo.getBankChannel();
        getBankFileService(bankChannel).filePush(pushVo);
    }

    public TrailResultVo repayTrail(RepayTrailVo repayTrailVo) {
        Loan loan = getLoanByIds(repayTrailVo.getLoanId(), repayTrailVo.getOuterLoanId());
        RepayApplyVo repayApplyVo = RepayConvert.INSTANCE.toRepayApplyVo(repayTrailVo);
        // 校验还款时间
        boolean isValid = getBankRepayService(loan.getChannel()).repayValidate(repayApplyVo);

        if (!isValid) {
            throw new BizException(BizErrorCode.REPAY_TIME_INVALID);
        }

        //判断是否存在处理中的还款&&代还
        int processingCount = customerRepayRecordRepository.countByLoanIdAndRepayStatusIn(loan.getId(), List.of(ProcessStatus.INIT, ProcessStatus.PROCESSING));
        Optional<BankBatchSubstituteRecord> optional = commonService.findBankSubstituteRecord(loan.getId(), repayTrailVo.getPeriod());
        BankBatchSubstituteRecord record = null;
        if (optional.isPresent()) {
            record = optional.get();
        }
        if (processingCount > 0 || record != null && record.getSubstituteStatus() == ProcessStatus.PROCESSING) {
            throw new BizException("60008", "还款处理中,请稍后试算");
        }

        return getBankRepayService(loan.getChannel()).trail(repayTrailVo);
    }

    /**
     * 还款申请
     *
     * @param bankRepayId 对资还款表
     */
    public void repayApply(String bankRepayId) {
        BankChannel bankChannel = commonService.findBankRepayRecord(bankRepayId).getChannel();
        getBankRepayService(bankChannel).bankRepayApplyMq(bankRepayId);
    }

    public RepayResultVo repayApply(RepayApplyVo repayApplyVo) {

        Loan loan = getLoanByIds(repayApplyVo.getLoanId(), repayApplyVo.getOuterLoanId());
        // 校验还款时间
        boolean isValid = getBankRepayService(loan.getChannel()).repayValidate(repayApplyVo);

        if (!isValid) {
            throw new BizException(BizErrorCode.REPAY_TIME_INVALID);
        }

        long between = ChronoUnit.DAYS.between(loan.getLoanTime().toLocalDate(), LocalDate.now());
        // 当天放款不允许立即还款
        if(between == 0) {
            logger.warn("当前操作还款方式：{}，不允许当前还款。放款信息：{},请求参数：{}",repayApplyVo.getRepayPurpose(), JSON.toJSONString(loan),JSON.toJSONString(repayApplyVo));
            throw new BizException(BizErrorCode.REPAY_TIME_INVALID);
        }

        List<CustomerRepayRecord> possiblyRepayRecords = commonService.findListSuccessRepayRecordByLoanIdAndPeriod(loan.getId(), repayApplyVo.getPeriod());

        int existRecordCount = possiblyRepayRecords.stream()
            .filter(c -> RepayType.CLAIM != c.getRepayType())
            .filter(c -> PartRepayType.PART != c.getPartRepayType()).toList().size();

        if (existRecordCount > 0) {
            RepayResultVo result = new RepayResultVo();
            result.setStatus(ProcessStatus.FAIL);
            result.setFailMsg("该期已还，请勿重复还款");
            return result;
        }

        Optional<BankBatchSubstituteRecord> optional = commonService.findBankSubstituteRecord(repayApplyVo.getLoanId(), repayApplyVo.getPeriod());
        BankBatchSubstituteRecord record = optional.orElse(null);

        if (record != null && record.getSubstituteStatus() == ProcessStatus.PROCESSING) {
            RepayResultVo result = new RepayResultVo();
            result.setStatus(ProcessStatus.FAIL);
            result.setFailMsg("还款处理中");
            return result;
        }

        // 对客还款记录
        CustomerRepayRecord customerRepayRecord = RepayConvert.INSTANCE.toCrrEntity(repayApplyVo);
        customerRepayRecord.setProjectCode(loan.getProjectCode());//项目唯一编码
        customerRepayRecord.setIsOverdue(repayApplyVo.getIsOverdue());//是否逾期
        customerRepayRecord.setChannel(loan.getChannel());
        customerRepayRecord.setLoanId(loan.getId());
        customerRepayRecord.setOuterRepayId(repayApplyVo.getOuterRepayId());
        customerRepayRecord.setRepayTime(LocalDateTime.now());
        customerRepayRecord.setRepayStatus(ProcessStatus.INIT);
        customerRepayRecord.setTransferDate(repayApplyVo.getTransferDate());
        customerRepayRecord.setReduceAmt(repayApplyVo.getReduceAmount() == null ? BigDecimal.ZERO : repayApplyVo.getReduceAmount());
        customerRepayRecord.setBreachAmt(repayApplyVo.getBreachFee() == null ? BigDecimal.ZERO : repayApplyVo.getBreachFee());
        customerRepayRecord.setChargeChannelId(repayApplyVo.getChargeChannelId());

        //保存还款卡信息
        customerRepayRecord.setAgreementNo(repayApplyVo.getAgreementNo());
        customerRepayRecord.setRepayBankCode(repayApplyVo.getRepayBankCode());
        customerRepayRecord.setRepayAcctNo(repayApplyVo.getRepayAcctNo());
        customerRepayRecord.setRepayRelUser(repayApplyVo.getRepayRelUser());
        customerRepayRecord.setRepayRelPhone(repayApplyVo.getRepayRelPhone());
        customerRepayRecord.setRepayRelCard(repayApplyVo.getRepayRelCard());
        customerRepayRecord = commonService.saveRepayRecord(customerRepayRecord);

        if (record != null && record.getSubstituteStatus() == ProcessStatus.SUCCESS) {
            return createCustomerRepayPlanSuccess(customerRepayRecord);
        }

        // 资方还款
        RepayResultVo repayResultVo = getBankRepayService(loan.getChannel()).apply(customerRepayRecord, repayApplyVo);

        // 对客还款记录
        customerRepayRecord.setRepayStatus(repayResultVo.getStatus());
        customerRepayRecord.setRemark(repayResultVo.getFailMsg());
        commonService.saveRepayRecord(customerRepayRecord);

        return repayResultVo;
    }

    private RepayResultVo createCustomerRepayPlanSuccess(CustomerRepayRecord repayRecord) {
        logger.info("save customer repay record and customer loan plan:{}", repayRecord.getId());
        LoanReplan loanReplan = commonService.findByLoanIdAndPeriod(repayRecord.getLoanId(), repayRecord.getPeriod());
        loanReplan.setCustRepayStatus(RepayStatus.REPAID);
        commonService.saveLoanReplan(loanReplan);

        // 对客还款计划
        CustomerLoanReplan customerLoanReplan = RepayConvert.INSTANCE.toPlanEntity(repayRecord);
        customerLoanReplan.setId(loanReplan.getId());
        customerLoanReplan.setRepayStatus(RepayStatus.REPAID);
        commonService.saveCustomerLoanReplan(customerLoanReplan);

        repayRecord.setRepayStatus(ProcessStatus.SUCCESS);
        repayRecord.setRepayTime(LocalDateTime.now());
        commonService.saveRepayRecord(repayRecord);

        RepayResultVo repayResultVo = new RepayResultVo();
        repayResultVo.setStatus(ProcessStatus.SUCCESS);
        return repayResultVo;
    }

    public RepayResultVo repayQuery(RepayQueryVo repayQueryVo) {
        String repayId = repayQueryVo.getRepayId();
        String outerRepayId = repayQueryVo.getOuterRepayId();
        // 查库
        CustomerRepayRecord customerRepayRecord = StringUtil.isNotBlank(repayId) ? commonService.findRepayRecordById(repayId)
            : commonService.findRepayRecordByOutId(outerRepayId);
        return (RepayResultVo) RepayConvert.INSTANCE.toVo(customerRepayRecord);
    }

    public void repayQuery(String repayId) {
        BankRepayRecord bankRepayRecord = commonService.findBankRepayRecord(repayId);
        getBankRepayService(bankRepayRecord.getChannel()).query(repayId);
    }

    public RepayBatchResultVo batchQueryResult(RepayQueryVo repayQueryVo) {
        String repayId = repayQueryVo.getRepayId();
        String outerRepayId = repayQueryVo.getOuterRepayId();
        // 查库
        BatchCustomerRepayRecord repayRecord = StringUtil.isNotBlank(repayId) ? commonService.findBatchRepayRecordById(repayId)
            : commonService.findBatchRepayRecordByOuterRepayId(outerRepayId);
        // 查询还款明细记录
        List<BatchCustomerRepayDetail> batchRepayDetail = commonService.findBatchRepayDetailByBatchRepayId(repayRecord.getId());
        RepayBatchResultVo batchQuery = RepayConvert.INSTANCE.toBatchQuery(repayRecord);
        // 查询流水号
        batchQuery.setRepayBatchInfo(RepayConvert.INSTANCE.toBatchQueryList(batchRepayDetail));
        // 查询对资还款明细
        String loanNo = CollectionUtil.isNotEmpty(batchRepayDetail) ? batchRepayDetail.get(0).getLoanNo() : StringUtils.EMPTY;
        if (Objects.nonNull(loanNo)) {
            batchQuery.setBankRepayRecordList(getBankRepayService(repayRecord.getChannel()).queryBankRepayRecord(loanNo, repayRecord));
        }

        return batchQuery;
    }

    public void batchRepayQuery(String repayId) {
        BatchCustomerRepayRecord repayRecord = commonService.findBatchRepayRecordById(repayId);
        getBankRepayService(repayRecord.getChannel()).batchRepayQuery(repayId);
    }

    /**
     * 对客已成功,通知资方还款
     *
     * @param customRepayId
     */
    public void retryBankRepayNotify(String customRepayId) {
        CustomerRepayRecord record = customerRepayRecordRepository.findById(customRepayId).orElseThrow(() -> new BizException(BizErrorCode.REPAY_NOT_FOUND));
        getBankRepayService(record.getChannel()).bankRepayNotify(record);
    }

    public void bankSubstituteApply(String substituteId) {
        BankBatchSubstituteRecord record = commonService.findBySubstituteId(substituteId);
        getBankRepayService(record.getChannel()).bankSubstituteApply(record);
    }

    /**
     * 对客已成功,通知资方还款
     *
     * @param processDTO
     */
    public void repayBankBatchNotify(BankProcessDTO processDTO) {
        getBankRepayService(processDTO.getChannel()).bankRepayBatchNotify(processDTO);
    }


    /**
     * 还款重试
     *
     * @param customRepayIds
     */
    public void repayCustomRetry(String customRepayIds) {


        String[] customRepayIdList = customRepayIds.split(",");
        for (String customRepayId : customRepayIdList) {
            if (StringUtil.isBlank(customRepayId)) {
                continue;
            }

            CustomerRepayRecord record = customerRepayRecordRepository.findById(customRepayId.trim())
                .orElseThrow(() -> new BizException(BizErrorCode.REPAY_NOT_FOUND));
            getBankRepayService(record.getChannel()).repayCustomRetry(record);

            try {
                Thread.sleep(SLEEP_MILLES);
            } catch (InterruptedException e) {
                logger.error("custom retry", e);
                // throw new RuntimeException(e);
            }

        }


    }

    public void claimApply(String bankLoanReplanId) {
        BankLoanReplan bankLoanReplan = commonService.findBankLoanReplan(bankLoanReplanId);
        getBankRepayService(bankLoanReplan.getChannel()).claimApply(bankLoanReplanId);
    }

    public void claimQuery(String bankRepayRecordId) {
        BankRepayRecord bankRepayRecord = commonService.findBankRepayRecord(bankRepayRecordId);
        getBankRepayService(bankRepayRecord.getChannel()).claimQuery(bankRepayRecordId);
    }

    /**
     * 流量方代偿申请
     *
     * @param claimId
     */

    public void flowClaimApply(String claimId) {
        FlowClaimRecord flowClaimRecord = commonService.findClaimRecordById(claimId);
        Loan loan = getLoanById(flowClaimRecord.getLoanId());
        getBankRepayService(loan.getChannel()).flowClaimApply(flowClaimRecord);
    }

    /**
     * 流量方代偿结果查询
     *
     * @param claimId
     */
    public void flowClaimQuery(String claimId) {
        FlowClaimRecord flowClaimRecord = commonService.findClaimRecordById(claimId);
        Loan loan = getLoanById(flowClaimRecord.getLoanId());
        getBankRepayService(loan.getChannel()).flowClaimQuery(flowClaimRecord);
    }

    public void claimAfterNotify(String claimAfterRecordId) {
        ClaimAfterRepayRecord afterRecord = commonService.findClaimAfterRecord(claimAfterRecordId);
        getBankRepayService(afterRecord.getChannel()).claimAfterNotify(claimAfterRecordId);
    }

    public CompensatedRepaySyncRlt compensatedRepaySync(CompensatedRepaySyncVo compensatedRepaySyncVo) {
        Loan loan = getLoanByIds(compensatedRepaySyncVo.getLoanId(), compensatedRepaySyncVo.getOuterLoanId());
        compensatedRepaySyncVo.setLoanId(loan.getId());
        compensatedRepaySyncVo.setOuterLoanId(loan.getOuterLoanId());
        return getBankRepayService(loan.getChannel()).compensatedRepaySync(compensatedRepaySyncVo);
    }

    public void covenantDownloadLoanContract(String loanId) {
        Loan loan = commonService.findLoanById(loanId);
        getBankFileService(loan.getChannel()).covenantDownloadLoanContract(loan);
    }

    public void covenantDownloadLoanVoucher(String loanId) {
        Loan loan = commonService.findLoanById(loanId);
        getBankFileService(loan.getChannel()).covenantDownloadLoanVoucher(loan);
    }

    public void covenantDownloadOther(String loanId) {
        Loan loan = commonService.findLoanById(loanId);
        getBankFileService(loan.getChannel()).covenantDownloadOther(loan);
    }

    public void covenantDownloadAgenda(String loanId) {
        Loan loan = commonService.findLoanById(loanId);
        getBankFileService(loan.getChannel()).covenantDownloadAgenda(null, null);
    }

    public void covenantDownloadAsyncApply(CovenantDownApplyDto dto) {
        Loan loan = commonService.findLoanById(dto.getLoanId());
        getBankFileService(loan.getChannel()).covenantDownloadAsyncApply(dto);
    }

    public void covenantDownloadAsyncQuery(String applyId) {
        commonService.findCovenantDownloadApplyRecordById(applyId).ifPresentOrElse(r -> {
            getBankFileService(r.getChannel()).covenantDownloadAsyncQuery(applyId);
        }, () -> logger.error("covenantDownloadAsyncQuery fail applyId:{}", applyId));
    }

    public void usingLetterQuery(String loanId) {
        Loan loan = commonService.findLoanById(loanId);
        getBankLoanService(loan.getChannel()).usingLetterQuery(loanId);
    }


    /**
     * 额度查询
     *
     * @param dto
     */
    public void quotaQuery(QuotaQueryDto dto) {
        BankChannel channel;
        if (QuotaStage.CREDIT.equals(dto.getStage())) {
            // 授信阶段
            channel = commonService.findCreditById(dto.getBusinessId()).getChannel();
        } else if (QuotaStage.LOAN.equals(dto.getStage())) {
            // 放款阶段
            channel = commonService.findLoanById(dto.getBusinessId()).getChannel();
        } else {
            // 调额阶段
            channel = commonService.findQuotaAdjustRecordById(dto.getBusinessId()).orElseThrow().getChannel();
        }
        getBankQuotaService(channel).quotaQuery(dto);
    }

    /**
     * 调额申请
     *
     * @param dto
     */
    public void quotaAdjustApply(QuotaAdjustApplyDto dto) {
        commonService.findQuotaAdjustRecordById(dto.getAdjustId()).ifPresentOrElse(r -> {
            getBankQuotaService(r.getChannel()).quotaAdjustApply(dto,r);
        }, () -> logger.error("quotaAdjustApply fail quotaAdjustRecord is null adjustId:{}", dto.getAdjustId()));
    }

    /**
     * 调额申请结果查询
     *
     * @param adjustId
     */
    public void quotaAdjustResultQuery(String adjustId) {
        commonService.findQuotaAdjustRecordById(adjustId).ifPresentOrElse(r -> {
            getBankQuotaService(r.getChannel()).quotaAdjustResultQuery(adjustId);
        }, () -> logger.error("quotaAdjustResultQuery fail quotaAdjustRecord is null adjustId:{}", adjustId));
    }

    /**
     * 额度查询结果回调
     *
     * @param dto
     * @param resultDto
     */
    public void quotaQueryResultCallback(QuotaQueryDto dto, QuotaQueryResultDto resultDto) {
        if (QuotaStage.CREDIT.equals(dto.getStage())) {
            // 授信阶段
            Credit credit = commonService.findCreditById(dto.getBusinessId());
            getBankCreditService(credit.getChannel()).quotaQueryResultCallback(dto, resultDto);
        } else if (QuotaStage.LOAN.equals(dto.getStage())) {
            // 放款阶段
            Loan loan = commonService.findLoanById(dto.getBusinessId());
            getBankLoanService(loan.getChannel()).quotaQueryResultCallback(dto, resultDto);
        } else {
            // 调额阶段
            QuotaAdjustRecord record = commonService.findQuotaAdjustRecordById(dto.getBusinessId()).orElseThrow();
            getBankQuotaService(record.getChannel()).quotaQueryResultCallback(dto, resultDto);
        }
    }

    /**
     * 调额结果回调
     *
     * @param adjustId
     */
    public void quotaAdjustResultCallback(String adjustId) {
        QuotaAdjustRecord record = commonService.findQuotaAdjustRecordById(adjustId).orElseThrow();
        if (QuotaStage.CREDIT.equals(record.getAdjustStage())) {
            // 授信阶段
            getBankCreditService(record.getChannel()).quotaAdjustResultCallback(adjustId);
        } else if (QuotaStage.LOAN.equals(record.getAdjustStage())) {
            // 放款阶段
            getBankLoanService(record.getChannel()).quotaAdjustResultCallback(adjustId);
        } else {
            // 无其他阶段
            logger.error("quota adjust not found stage，stage error adjustId:{}", adjustId);
        }
    }


    /**
     * 授信结果回调
     *
     * @param channel
     * @param json
     */
    public String creditResultCallback(BankChannel channel, String json) {
        return getBankCallbackService(channel).creditResultCallback(json);
    }

    /**
     * 放款结果回调
     *
     * @param channel
     * @param json
     */
    public String loanResultCallback(BankChannel channel, String json) {
        return getBankCallbackService(channel).loanResultCallback(json);
    }

    /**
     * 调额结果回调
     *
     * @param channel
     * @param json
     */
    public String bankQuotaAdjustResultCallback(BankChannel channel, String json) {
        return getBankCallbackService(channel).quotaAdjustResultCallback(json);
    }

    /**
     * 还款结果回调
     *
     * @param channel
     * @param json
     */
    public String repayResultCallback(BankChannel channel, String json) {
        return getBankCallbackService(channel).repayResultCallback(json);
    }

    /**
     * 资方对账通知
     *
     * @param channel
     * @param json
     */
    public String rccResultCallback(BankChannel channel, String json) {
        return getBankCallbackService(channel).rccResultCallback(json);
    }

    /**
     * 用户信息更新申请
     *
     * @param dto
     */
    public void userInfoUpdateApply(UserInfoUpdateDto dto) {
        if (LoanStage.CREDIT.equals(dto.getStage())) {
            Credit credit = commonService.findCreditById(dto.getBusinessId());
            getBankCreditService(credit.getChannel()).userInfoUpdateApply(credit);
        }
    }

    /**
     * 用户信息更新申请结果查询
     *
     * @param dto
     */
    public void userInfoUpdateQuery(UserInfoUpdateDto dto) {
        if (LoanStage.CREDIT.equals(dto.getStage())) {
            Credit credit = commonService.findCreditById(dto.getBusinessId());
            getBankCreditService(credit.getChannel()).userInfoUpdateQuery(credit);
        }
    }


    /**
     * 获取资方回调服务.
     *
     * @param channel 资方通道
     * @return 回调服务
     */
    private BankCallbackService getBankCallbackService(BankChannel channel) {
        if (null == bankCallbackServices) {
            logger.error("the callback service choose, bank channel is not found: {}", channel);
            throw new BizException(BizErrorCode.CHANNEL_NOT_FOUND);
        }
        for (BankCallbackService service : bankCallbackServices) {
            if (service.isSupport(channel)) {
                return service;
            }
        }
        throw new BizException(BizErrorCode.CHANNEL_NOT_FOUND);
    }

    private BankProtocolSyncService getBankProtocolService(BankChannel channel) {
        if (null == bankProtocolSyncServices) {
            logger.error("the file service choose, bank channel is not found: {}", channel);
            throw new BizException(BizErrorCode.CHANNEL_NOT_FOUND);
        }
        for (BankProtocolSyncService service : bankProtocolSyncServices) {
            if (service.isSupport(channel)) {
                return service;
            }
        }
        throw new BizException(BizErrorCode.CHANNEL_NOT_FOUND);
    }


    /**
     * 获取资方授信服务.
     *
     * @param channel 资方通道
     * @return 授信服务
     */
    private BankCreditService getBankCreditService(BankChannel channel) {
        if (null == bankCreditServices) {
            logger.error("the credit service choose, bank channel is not found: {}", channel);
            throw new BizException(BizErrorCode.CHANNEL_NOT_FOUND);
        }
        for (BankCreditService service : bankCreditServices) {
            if (service.isSupport(channel)) {
                return service;
            }
        }
        throw new BizException(BizErrorCode.CHANNEL_NOT_FOUND);
    }

    /**
     * 获取资方放款服务.
     *
     * @param channel 资方通道
     * @return 授信服务
     */
    private BankLoanService getBankLoanService(BankChannel channel) {
        if (null == bankLoanServices) {
            logger.error("the loan service choose, bank channel is not found: {}", channel);
            throw new BizException(BizErrorCode.CHANNEL_NOT_FOUND);
        }
        for (BankLoanService service : bankLoanServices) {
            if (service.isSupport(channel)) {
                return service;
            }
        }
        throw new BizException(BizErrorCode.CHANNEL_NOT_FOUND);
    }

    private BankPlanService getBankPlanService(BankChannel channel) {
        if (null == bankPlanServices) {
            logger.error("the plan service choose, bank channel is not found: {}", channel);
            throw new BizException(BizErrorCode.CHANNEL_NOT_FOUND);
        }
        for (BankPlanService service : bankPlanServices) {
            if (service.isSupport(channel)) {
                return service;
            }
        }
        throw new BizException(BizErrorCode.CHANNEL_NOT_FOUND);
    }

    /**
     * 获取资方还款服务.
     *
     * @param channel 资方通道
     * @return 还款服务
     */
    private BankRepayService getBankRepayService(BankChannel channel) {
        if (null == bankRepayServices) {
            logger.error("the repay service choose, bank channel is not found: {}", channel);
            throw new BizException(BizErrorCode.CHANNEL_NOT_FOUND);
        }
        for (BankRepayService service : bankRepayServices) {
            if (service.isSupport(channel)) {
                return service;
            }
        }
        throw new BizException(BizErrorCode.CHANNEL_NOT_FOUND);
    }


    /**
     * 获取资方对账服务.
     *
     * @param channel 资方通道
     * @return 对账服务
     */
    private BankReccService getBankReccService(BankChannel channel) {
        if (null == bankReccServices) {
            logger.error("the recc service choose, bank channel is not found: {}", channel);
            throw new BizException(BizErrorCode.CHANNEL_NOT_FOUND);
        }
        for (BankReccService service : bankReccServices) {
            if (service.isSupport(channel)) {
                return service;
            }
        }
        throw new BizException(BizErrorCode.CHANNEL_NOT_FOUND);
    }




    /**
     * 获取资方绑卡服务.
     *
     * @param channel 资方通道
     * @return 授信服务
     */
    private BankBindService getBankBindService(BankChannel channel) {
        if (null == bankBindServices) {
            logger.error("the bind service choose, bank channel is not found: {}", channel);
            throw new BizException(BizErrorCode.CHANNEL_NOT_FOUND);
        }
        for (BankBindService service : bankBindServices) {
            if (service.isSupport(channel)) {
                return service;
            }
        }
        throw new BizException(BizErrorCode.CHANNEL_NOT_FOUND);
    }


    /**
     * 获取资方绑卡服务.
     *
     * @param channel 资方通道
     * @return 授信服务
     */
    private BankContractInfoService getBankContractInfoService(BankChannel channel) {
        if (null == bankContractInfoServices) {
            logger.error("the bank contract service choose, bank channel is not found: {}", channel);
            throw new BizException(BizErrorCode.CHANNEL_NOT_FOUND);
        }
        for (BankContractInfoService service : bankContractInfoServices) {
            if (service.isSupport(channel)) {
                return service;
            }
        }
        throw new BizException(BizErrorCode.CHANNEL_NOT_FOUND);
    }

    /**
     * 获取资方额度服务
     *
     * @param channel 资方通道
     * @return 额度服务
     */
    private BankQuotaService getBankQuotaService(BankChannel channel) {
        if (null == bankQuotaServices) {
            logger.error("the bank quota service choose, bank channel is not found: {}", channel);
            throw new BizException(BizErrorCode.CHANNEL_NOT_FOUND);
        }
        for (BankQuotaService service : bankQuotaServices) {
            if (service.isSupport(channel)) {
                return service;
            }
        }
        throw new BizException(BizErrorCode.CHANNEL_NOT_FOUND);
    }

    private BankFileService getBankFileService(BankChannel channel) {
        if (null == bankFileServices) {
            logger.error("the file service choose, bank channel is not found: {}", channel);
            throw new BizException(BizErrorCode.CHANNEL_NOT_FOUND);
        }
        for (BankFileService service : bankFileServices) {
            if (service.isSupport(channel)) {
                return service;
            }
        }
        throw new BizException(BizErrorCode.CHANNEL_NOT_FOUND);
    }





    public SignApplyReq getContractInfoTemplate(final BankChannel channel, final String businessId, AgreementSignature signature) {
        return getBankContractInfoService(channel).fetchTemplateSignParam(businessId, signature);
    }


    public ReccResultVo processRecc(ReccApplyVo applyVo) {
        BankChannel channel = applyVo.getChannel();

        getBankReccService(channel).process(applyVo);
        ReccResultVo result = new ReccResultVo();
        result.setActReccTime(LocalDateTime.now());
        result.setStatus(ProcessStatus.SUCCESS);
        return result;
    }

    public ReccResultVo query(ReccApplyVo applyVo) {
        BankChannel channel = applyVo.getChannel();
        return getBankReccService(channel).query(applyVo);
    }

    public ReccResultVo download(ReccDownloadVo vo) {
        BankChannel channel = vo.getChannel();
        return getBankReccService(channel).download(vo);
    }

    public List<CYBKReconcileFile> fileApply(ReccFileApplyVo vo) {
        BankChannel channel = vo.getChannel();
        return getBankReccService(channel).fileApply(vo);
    }

    public ClaimMarkResultVo claimMark(ClaimMarkApplyVo applyVo) {
        BankChannel channel = applyVo.getChannel();
        return getBankRepayService(channel).claimMark(applyVo);
    }

    public ClaimRetryResultVo claimRetry(ClaimRetryVo claimRetryVo) {
        BankChannel channel = claimRetryVo.getBankChannel();
        return getBankRepayService(channel).claimRetry(claimRetryVo);
    }

    public RepayNoticeResultVo repayNotice(RepayNoticeVo repayNoticeVo) {
        return getBankRepayService(repayNoticeVo.getBankChannel()).repayNotice(repayNoticeVo);
    }

    /**
     * xxl-job调用
     *
     * @param vo
     * @return
     */
    public ActiveLaunchClaimResultVo activeLaunchClaim(ActiveLaunchClaimApplyVo vo) {
        return getBankRepayService(vo.getChannel()).activeLaunchClaim(vo);
    }


    /**
     * 业务系统调用 代偿申请
     *
     * @param vo
     * @return
     */
    public ActiveClaimApplyResultVo activeClaimApply(ActiveClaimApplyVo vo) {
        Loan loan = getLoanByIds(vo.getLoanId(), vo.getOuterLoanId());
        vo.setLoanId(loan.getId());
        return getBankRepayService(loan.getChannel()).activeClaimApply(vo);
    }

    /**
     * 业务系统调用 代偿结果查询
     *
     * @param vo
     * @return
     */
    public ActiveClaimQueryResultVo activeClaimQuery(ActiveClaimQueryVo vo) {
        FlowClaimRecord flowClaimRecord = null != vo.getClaimId()
            ? commonService.findClaimRecordById(vo.getClaimId())
            : commonService.findClaimRecordByOuterClaimId(vo.getOuterClaimId());
        if (null == flowClaimRecord) {
            ActiveClaimQueryResultVo resultVo = new ActiveClaimQueryResultVo();
            resultVo.setStatus(ProcessStatus.FAIL);
            resultVo.setClaimResult(ClaimResult.NON_EXISTENT);
            resultVo.setMsg(ClaimResult.NON_EXISTENT.getDesc());
            return resultVo;
        }
        vo.setClaimId(flowClaimRecord.getId());
        return getBankRepayService(flowClaimRecord.getChannel()).activeClaimQuery(vo);
    }

    public RepayDateResultVo appropriationInterest(RepayDateApplyVo applyVo) {
        BankChannel channel = applyVo.getChannel();
        return getBankLoanService(channel).appropriationInterest(applyVo);
    }

    public LoanLprResultVo lprQuery(LoanLprQueryVo applyVo) {
        Credit credit = StringUtil.isNotBlank(applyVo.getCreditId())
            ? commonService.findCreditById(applyVo.getCreditId()) : commonService.findCreditByOutId(applyVo.getSysCreditId());
        return switch (credit.getChannel()) {
           // case QJ_LSB, QJ_HRB, QJ_YL_F003, YL_QJ, LH_RL_QH, HAIER_LH_QH -> getBankLprService(credit.getChannel()).lpr(credit);
            default -> getBankLoanService(credit.getChannel()).lpr(credit);
        };
    }

    public LoanTrialResultVo loanTrial(LoanTrialQueryVo loanTrialQueryVo) {
        return getBankLoanService(loanTrialQueryVo.getBankChannel()).loanTrial(loanTrialQueryVo);
    }

    public LoanNoticeResultVo loanNotice(LoanNoticeVo loanNoticeVo) {
        return getBankLoanService(loanNoticeVo.getBankChannel()).loanNotice(loanNoticeVo);
    }

    public BusinessChronosProcessResultVo loanCotractNoQuery(BusinessChronosProcessVo processVo) {
        return getBankLoanService(processVo.getBankChannel()).loanContractNoQuery(processVo);
    }

    public void planPushDetail(String loanId, String sysLoanId, PlanVo planVo) {
        BankChannel channel = planVo.getChannel();
        Loan loan = StringUtil.isNotBlank(loanId)
            ? commonService.findLoanById(loanId) : commonService.findLoanByOutId(sysLoanId);
        planVo.setLoanId(loan.getId());
        getBankPlanService(channel).planDetailPush(planVo);
    }

    public void processDailyFile(FileDailyProcessVo processVo) {
        BankChannel bankChannel = processVo.getBankChannel();
        getBankFileService(bankChannel).processDaily(processVo);
    }


    public void batchVoucherDownload(FileDailyProcessVo processVo) {
        BankChannel bankChannel = processVo.getBankChannel();
        getBankFileService(bankChannel).batchVoucherDownload(processVo);
    }

    /**
     * 绑卡申请
     *
     * @param bindApplyVo 绑卡申请
     * @return 结果
     */
    public BindResultVo bindApply(BindApplyVo bindApplyVo) {
        BankChannel bankChannel = bindApplyVo.getBankChannel();
        return getBankBindService(bankChannel).apply(bindApplyVo);
    }


    /**
     * 绑卡确认
     *
     * @param bindConfirmVo 绑卡确认
     * @return 结果
     */
    public BindResultVo bindConfirm(BindConfirmVo bindConfirmVo) {
        BankChannel bankChannel = bindConfirmVo.getBankChannel();
        return getBankBindService(bankChannel).confirm(bindConfirmVo);
    }

    public CommonService getCommonService() {
        return commonService;
    }


    /**
     * 重新授信
     */
    public CreditResultVo recreditApply(RecreditApplyVo recreditApply) {
        BankChannel bankChannel = recreditApply.getBankChannel();
        return getBankCreditService(bankChannel).recreditApply(recreditApply);
    }

    public void fileUploadNotify(String creditId) {
        Credit credit = commonService.findCreditById(creditId);
        getBankLoanService(credit.getChannel()).fileUploadNotify(creditId);
    }

    public void fileDownloadRequest(FileDownloadRequestVo downloadRequestVo) {
        BankChannel bankChannel = downloadRequestVo.getBankChannel();
        getBankFileService(bankChannel).downloadRequest(downloadRequestVo);
    }



    public void loanUploadImage(String accountId) {
        Credit credit = commonService.findCreditByAccountId(accountId);
        BankChannel bankChannel = credit.getChannel();
        FileUploadVo fileUploadVo = new FileUploadVo();
        fileUploadVo.setLoanOrderId(credit.getId());
        fileUploadVo.setBankChannel(bankChannel);
        getBankFileService(bankChannel).loanUploadImage(fileUploadVo);
    }


    public void creditAgreementRecreate(String creditId) {
        Credit credit = commonService.findCreditById(creditId);
        getBankCreditService(credit.getChannel()).creditAgreementRecreate(credit);
    }

    public PreCreditApplyResultVo preCredit(PreCreditApplyVo preCreditApplyVo) {
        BankChannel bankChannel = preCreditApplyVo.getBankChannel();
        return getBankCreditService(bankChannel).preApply(preCreditApplyVo);
    }

    /**
     * 批量扣款
     */
    public RepayResultVo batchApply(RepayDeductionApplyVo repayApplyVo) {
        List<RepayPlanItemVo> applyList = repayApplyVo.getApplyList();

        List<String> loanIds = applyList.stream()
            .map(RepayPlanItemVo::getLoanId)
            .toList();

        // 基础校验
        Loan loan = getLoan(repayApplyVo, applyList, loanIds);

        // 校验是否已还款
        for (RepayPlanItemVo planItemVo : repayApplyVo.getApplyList()) {
            List<BatchCustomerRepayDetail> repayDetails = commonService.findByLoanNoAndPeriodAndStatus(
                planItemVo.getLoanId(), planItemVo.getPeriod(), ProcessStatus.SUCCESS);

            boolean existRecord = repayDetails.stream()
                .anyMatch(c -> RepayType.CLAIM != c.getRepayType());

            if (existRecord) {
                RepayResultVo result = new RepayResultVo();
                result.setStatus(ProcessStatus.FAIL);
                result.setFailMsg("借据：" + planItemVo.getLoanId() + "已还，请勿重复还款");
                return result;
            }
        }

        Map<String, RepayPlanItemVo> repayPlanItem = applyList.stream()
            .collect(Collectors.toMap(
                item -> item.getLoanId() + "_" + item.getPeriod(),
                Function.identity()
            ));

        // 还款试算
        BatchTrialResultVo batchTrialResultVo = getBankRepayService(loan.getChannel()).buildBatchCustomerTrialResult(loan, repayApplyVo);
        List<BatchTrailDetailsVo> batchTrailDetails = batchTrialResultVo.getBatchTrailDetails();

        // 保存还款请求数据
        BatchCustomerRepayRecord repayRecord = saveBatchCustomerRepayRecord(repayApplyVo, batchTrailDetails, loan);

        // 保存还款请求明细
        saveAllRepayDetails(repayApplyVo, loan, repayPlanItem, repayRecord, batchTrailDetails);

        // 资方还款
        RepayResultVo repayResultVo = getBankRepayService(loan.getChannel()).batchApply(repayRecord.getId());

        // 批量还款记录
        repayRecord.setStatus(repayResultVo.getStatus());
        repayRecord.setRemark(repayResultVo.getFailMsg());
        commonService.saveBatchRepayRecord(repayRecord);
        return repayResultVo;
    }




    private void saveAllRepayDetails(RepayDeductionApplyVo repayApplyVo, Loan loan, Map<String, RepayPlanItemVo> repayPlanItem,
                                     BatchCustomerRepayRecord repayRecord, List<BatchTrailDetailsVo> batchTrailDetails) {
        //初始化批量扣款明细
        List<BatchCustomerRepayDetail> repayDetails = new ArrayList<>();
        for (BatchTrailDetailsVo trailDetail : batchTrailDetails) {
            BatchCustomerRepayDetail detail = new BatchCustomerRepayDetail();
            detail.setBatchRepayId(repayRecord.getId());
            detail.setChannel(loan.getChannel());
            detail.setPeriod(trailDetail.getPeriod());
            detail.setRepayPurpose(repayApplyVo.getRepayPurpose());
            detail.setRepayMode(repayApplyVo.getRepayMode());
            detail.setRepayType(repayApplyVo.getRepayType());
            detail.setRepayCategory(repayApplyVo.getRepayCategory());
            detail.setPrincipalAmt(trailDetail.getPrincipal());
            detail.setInterestAmt(trailDetail.getInterest());
            detail.setPenaltyAmt(trailDetail.getOverdueFee());
            detail.setReductAmt(trailDetail.getReduceAmt());
            detail.setBreachAmt(trailDetail.getBreachFee());
            detail.setGuaranteeFee(trailDetail.getGuaranteeFee());
            detail.setTotalAmt(trailDetail.getAmount());
            detail.setBankNo(repayApplyVo.getRepayAcctNo());
            detail.setAccountName(repayApplyVo.getRepayRelUser());
            detail.setLoanNo(trailDetail.getLoanId());
            detail.setStatus(ProcessStatus.INIT);
            String key = trailDetail.getLoanId() + "_" + trailDetail.getPeriod();
            detail.setTranNo(repayPlanItem.get(key).getTranNo());
            repayDetails.add(detail);
        }
        commonService.saveAllRepayDetails(repayDetails);
    }

    private BatchCustomerRepayRecord saveBatchCustomerRepayRecord(RepayDeductionApplyVo repayApplyVo, List<BatchTrailDetailsVo> batchTrailDetails, Loan loan) {
        //初始化批量扣款记录
        BatchCustomerRepayRecord repayRecord = new BatchCustomerRepayRecord();
        repayRecord.setTotalAmt(AmountUtil.calcSumAmount(batchTrailDetails, BatchTrailDetailsVo::getAmount));
        repayRecord.setPrincipalAmt(AmountUtil.calcSumAmount(batchTrailDetails, BatchTrailDetailsVo::getPrincipal));
        repayRecord.setInterestAmt(AmountUtil.calcSumAmount(batchTrailDetails, BatchTrailDetailsVo::getInterest));
        repayRecord.setPenaltyAmt(AmountUtil.calcSumAmount(batchTrailDetails, BatchTrailDetailsVo::getOverdueFee));
        repayRecord.setBreachAmt(AmountUtil.calcSumAmount(batchTrailDetails, BatchTrailDetailsVo::getBreachFee));
        repayRecord.setGuaranteeFee(AmountUtil.calcSumAmount(batchTrailDetails, BatchTrailDetailsVo::getGuaranteeFee));
        repayRecord.setReduceAmt(repayApplyVo.getTotalReduceAmt());
        repayRecord.setOuterRepayId(repayApplyVo.getOuterRepayId());
        repayRecord.setChannel(loan.getChannel());
        repayRecord.setStatus(ProcessStatus.INIT);
        repayRecord.setRepayType(repayApplyVo.getRepayType());
        repayRecord.setRepayMode(repayApplyVo.getRepayMode());
        repayRecord.setRepayPurpose(repayApplyVo.getRepayPurpose());
        repayRecord.setRepayCategory(repayApplyVo.getRepayCategory());
        repayRecord.setAgreementNo(repayApplyVo.getAgreementNo());
        repayRecord.setRepayBankCode(repayApplyVo.getRepayBankCode());
        repayRecord.setRepayAcctNo(repayApplyVo.getRepayAcctNo());
        repayRecord.setRepayRelUser(repayApplyVo.getRepayRelUser());
        repayRecord.setRepayRelPhone(repayApplyVo.getRepayRelPhone());
        repayRecord.setRepayRelCard(repayApplyVo.getRepayRelCard());
        repayRecord = commonService.saveBatchRepayRecord(repayRecord);
        return repayRecord;
    }

    private Loan getLoan(RepayDeductionApplyVo repayApplyVo, List<RepayPlanItemVo> applyList, List<String> loanIds) {
        if (RepayType.REPAY.equals(repayApplyVo.getRepayType())) {
            // 放款日当天不允许还款
            long count = commonService.findByLoanAndIdsAndLoanTime(loanIds, LocalDate.now());

            if (count > 0) {
                throw new BizException(BizErrorCode.REPAYMENT_NOT_ALLOWED);
            }
        }
        //获取资金渠道
        Loan loan = getLoanById(applyList.get(0).getLoanId());

        // 校验还款时间
        boolean isValid = getBankRepayService(loan.getChannel()).batchApplyRepayValidate(repayApplyVo);

        if (!isValid) {
            throw new BizException(BizErrorCode.REPAY_TIME_INVALID);
        }
        return loan;
    }

    public BigDecimal safeNum(BigDecimal num) {
        return num == null ? BigDecimal.ZERO : num;
    }

    public void batchRepayApply(String batchRepayId) {
        BatchCustomerRepayRecord repayRecord = commonService.findBatchRepayRecordById(batchRepayId);
        getBankRepayService(repayRecord.getChannel()).batchRepayApply(repayRecord);
    }




    public BatchTrialResultVo batchTrial(BatchTrailVo trailVo) {
        BankChannel bankChannel = trailVo.getChannel();
        return getBankRepayService(bankChannel).batchTrial(trailVo);
    }

    public void planOverdue(PlanOverdueVo planOverdueVo) {
        BankChannel bankChannel = planOverdueVo.getBankChannel();
        getBankRepayService(bankChannel).planOverdue(planOverdueVo);
    }

    public void trustPlanRelease(TrustPlanReleaseVo trustPlanReleaseVo) {
        BankChannel bankChannel = trustPlanReleaseVo.getBankChannel();
        getBankRepayService(bankChannel).trustPlanRelease(trustPlanReleaseVo);
    }

    public void loanAfterAgreementSign(String loanId) {
        Loan loan = commonService.findLoanById(loanId);

        getBankLoanService(loan.getChannel()).loanAfterAgreementSign(loan);
    }





    public DefrayResultVo defray(DefrayVo defrayVo) {
        BankChannel bankChannel = defrayVo.getBankChannel();
        return getBankRepayService(bankChannel).defray(defrayVo);
    }

    public SubstituteMarkResultVo substituteMark(SubstituteMarkApplyVo substituteMarkApplyVo) {
        BankChannel bankChannel = substituteMarkApplyVo.getBankChannel();
        return getBankRepayService(bankChannel).substituteMark(substituteMarkApplyVo);
    }

    public SubstituteApplyResultVo substituteApply(SubstituteApplyVo substituteApplyVo) {
        BankChannel bankChannel = substituteApplyVo.getBankChannel();
        return getBankRepayService(bankChannel).substituteApply(substituteApplyVo);
    }

    public SubstituteApplyResultVo handSubstituteApply(SubstituteApplyVo substituteApplyVo) {
        BankChannel bankChannel = substituteApplyVo.getBankChannel();
        return getBankRepayService(bankChannel).handSubstituteApply(substituteApplyVo);
    }


    public FileSynRepayPlanResultDVo synRepayPlanFile(FileSynRepayPlanVo fileSynRepayPlanVo) {
        BankChannel bankChannel = fileSynRepayPlanVo.getBankChannel();
        return getBankFileService(bankChannel).synRepayPlanFile(fileSynRepayPlanVo);
    }

    public FileSyncDueFileResultVo syncDueFile(FileSyncDueFileVo fileSyncDueFileVo) {
        BankChannel bankChannel = fileSyncDueFileVo.getBankChannel();
        return getBankFileService(bankChannel).syncDueFile(fileSyncDueFileVo);
    }



    public BankPlanQueryRltVo bankPlanQuery(BankPlanQueryVo planQueryVo) {
        BankChannel bankChannel = planQueryVo.getBankChannel();
        return getBankRepayService(bankChannel).bankPlanQuery(planQueryVo);
    }

    /**
     * 账单确认
     *
     * @param vo
     * @return
     */
    public ReccResultVo confirm(RccConfirmVo vo) {
        BankChannel bankChannel = vo.getChannel();
        return getBankRepayService(bankChannel).confirm(vo);
    }


    /**
     * 查询账单详情
     *
     * @param vo
     * @return
     */
    public ReccDetailQueryResultVo queryRccDetail(ReccDetailQueryVo vo) {
        BankChannel bankChannel = vo.getChannel();
        return getBankRepayService(bankChannel).queryRccDetail(vo);
    }




    /**
     * 取消订单
     *
     * @param vo
     * @return
     */
    public OrderCancelResultVo orderCancel(OrderCancelVo vo) {
        BankChannel bankChannel = vo.getBankChannel();
        return getBankLoanService(bankChannel).orderCancel(vo);
    }

    public RepayReturnUploadResultDto handleOfflineRepayReturnFileUpload(RepayReturnUploadVo applyVo) {
        return getBankFileService(applyVo.getBankChannel()).offlineRepayReturnFileUpload(applyVo);
    }
}
