package com.jinghang.cash.convert;

import com.fasterxml.jackson.core.JsonParser;
import com.fasterxml.jackson.databind.DeserializationContext;
import com.fasterxml.jackson.databind.JsonDeserializer;
import com.fasterxml.jackson.databind.JsonNode;
import com.jinghang.cash.enums.FileType;

import java.io.IOException;

public class FileTypeDeserializer extends JsonDeserializer<FileType> {
    
    @Override
    public FileType deserialize(JsonParser p, DeserializationContext ctxt) 
            throws IOException {
        
        JsonNode node = p.getCodec().readTree(p);
        
        // 处理字符串值
        if (node.isTextual()) {
            return FileType.fromValue(node.asText());
        }
        
        // 处理JSON对象值
        if (node.isObject()) {
            JsonNode contractTemplateType = node.get("contractTemplateType");
            if (contractTemplateType != null && contractTemplateType.isTextual()) {
                return FileType.fromDescName(contractTemplateType.asText());
            }
        }
        
        throw new IllegalArgumentException("无法解析 FileType: " + node);
    }
}