package com.jinghang.cash.modules.project.domain;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.bean.copier.CopyOptions;
import com.baomidou.mybatisplus.annotation.*;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.jinghang.cash.api.enums.AbleStatusExt;
import com.jinghang.cash.api.enums.ActiveInactive;
import com.jinghang.cash.api.enums.FileType;
import com.jinghang.cash.modules.project.domain.dto.ProjectAgreementDto;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 协议模板配置实体
 *
 * @Author: Lior
 * @CreateTime: 2025/8/25 09:30
 */
@TableName(value = "project_agreement")
@Getter
@Setter
public class ProjectAgreement implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    @TableId
    private String id;

    /**
     * 项目唯一编码
     */
    @TableField(value = "project_code")
    private String projectCode;

    /**
     * 合同模板唯一编码
     */
    @TableField(value = "template_code")
    private String templateCode;

    /**
     * 合同模板类型
     */
    @TableField(value = "contract_template_type")
    private FileType contractTemplateType;

    /**
     * 合同模板类型描述
     */
    @TableField(value = "contract_template_desc")
    private String contractTemplateDesc;

    /**
     * 资产方合同签署阶段
     */
    @TableField(value = "flow_loan_stage")
    private String flowLoanStage;

    /**
     * 资金方合同签署阶段
     */
    @TableField(value = "capital_loan_stage")
    private String capitalLoanStage;

    /**
     * 模板归属方
     */
    @TableField(value = "template_owner")
    private String templateOwner;

    /**
     * 模板归属方名称
     */
    @TableField(value = "template_owner_name")
    private String templateOwnerName;

    /**
     * 资金方合同名称
     */
    @TableField(value = "capital_contract_name")
    private String capitalContractName;

    /**
     * 资产方合同名称
     */
    @TableField(value = "flow_contract_name")
    private String flowContractName;

    /**
     * 是否融担签章
     */
    @TableField(value = "is_rd_signature")
    private ActiveInactive isRdSignature;

    /**
     * 签章类型
     */
    @TableField(value = "seal_type")
    private String sealType;

    /**
     * 签章系统唯一编码
     */
    @TableField(value = "template_no")
    private String templateNo;

    /**
     * 是否回传资金方
     */
    @TableField(value = "is_return_to_capital")
    private ActiveInactive isReturnToCapital;

    /**
     * 是否回传流量方
     */
    @TableField(value = "is_return_to_flow")
    private ActiveInactive isReturnToFlow;

    /**
     * 模板备注
     */
    @TableField(value = "remark")
    private String remark;

    /**
     * 启用状态
     */
    @TableField(value = "enabled")
    private AbleStatusExt enabled;

    /**
     * 乐观锁
     */
    @TableField(value = "revision", fill = FieldFill.INSERT)
    @Version
    private Integer revision;

    /**
     * 创建人
     */
    @TableField(value = "created_by", fill = FieldFill.INSERT)
    private String createdBy;

    /**
     * 创建时间
     */
    @TableField(value = "created_time", fill = FieldFill.INSERT)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime createdTime;

    /**
     * 更新人
     */
    @TableField(value = "updated_by", fill = FieldFill.UPDATE)
    private String updatedBy;

    /**
     * 更新时间
     */
    @TableField(value = "updated_time", fill = FieldFill.UPDATE)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime updatedTime;

    /**
     *  删除状态
     */
    private String deleted;

    /**
     * 文件路径
     */
    private String filePath;

    public void copy(ProjectAgreement source){
        BeanUtil.copyProperties(source,this, CopyOptions.create().setIgnoreNullValue(true));
    }

    public void copy(ProjectAgreementDto source){
        //String[] ignoreProperties = {"contractTemplateType", "isRdSignature", "isReturnToCapital", "isReturnToFlow"};
        BeanUtil.copyProperties(source,this, CopyOptions.create().setIgnoreNullValue(true));
    }
}
