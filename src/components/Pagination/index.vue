<template>
  <div v-show="total>0" class="pagingBox">
    <!-- //分页器封装文件 -->
    <el-pagination v-if='total > 0' @size-change="sizeChange" @current-change="currentChange" :page-size="pageSize"
      layout="total, sizes, prev, pager, next, jumper" :current-page.sync="pageNum" :total="total">
    </el-pagination>
  </div>
</template>

<script>
  export default {
    name: "Pagination",
    data() {
      return {
        pageNum: 1, //当前页数为第一页
        pageSizes: [5, 10, 15, 20], //每页显示多少条
      };
    },
    //子组件接收父组件的值
    props: {
      total: {
        //总数据条数
        required: false, //是否必填
        default: 0, //默认值为0
      },
      pageSize: {
        required: false, //是否必填
        default: 10, //默认显示10条
      },
    },
    watch: {
      //监听页数变化
      pageNum(val, index) {
        if (typeof val === "number") {
          this.pageNum = val;
        }
      },
    },
    // 事件方法
    methods: {
      // 每页展示几条变化
      sizeChange(val) {
        //子组件向父组件传值
        this.$emit("sizeChange", val);
      },
      // 页码变化
      currentChange(val) {
        //子组件向父组件传值
        this.$emit("currentChange", val);
      },
    },
  };
</script>
<style scoped>
  .pagingBox {
    margin-top: 20px;
    text-align: right;
  }
</style>
