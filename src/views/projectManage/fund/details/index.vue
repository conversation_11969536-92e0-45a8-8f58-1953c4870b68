<template>
    <div class="app-container">
      <el-collapse v-model="activeNames">
        <el-collapse-item title="资金方详情" name="1">
          <el-form ref="form" :model="form" :rules="rules" size="small" label-width="120px"">
            <el-row :gutter="24">
              <el-col :span="8">
                <el-form-item label="资金方编码" prop="bankChannel">
                  <el-input v-model="form.bankChannel" style="width: 280px;" disabled/>
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item label="资金方简称" prop="capitalNameShort">
                  <el-input v-model="form.capitalNameShort" style="width: 280px;" :disabled="type === 'detail'"/>
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item label="资金方公司名称" prop="capitalName">
                  <el-input v-model="form.capitalName" style="width: 280px;" :disabled="type === 'detail'"/>
                </el-form-item>
              </el-col>
            </el-row>
            <el-row :gutter="24">
              <el-col :span="8">
                <el-form-item label="主营产品" prop="mainProd">
                  <el-input v-model="form.mainProd" style="width: 280px;" :disabled="type === 'detail'"/>
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item label="联系人" prop="contactPerson">
                  <el-input v-model="form.contactPerson" style="width: 280px;" :disabled="type === 'detail'"/>
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item label="联系方式" prop="contactPhone">
                  <el-input v-model="form.contactPhone" style="width: 280px;" :disabled="type === 'detail'"/>
                </el-form-item>
              </el-col>
            </el-row>
            <el-row :gutter="24">
              <el-col :span="8">
                <el-form-item label="资金方接入日期" prop="startDate">
                  <el-date-picker
                    value-format = "yyyy-MM-dd"
                    v-model="form.startDate"
                    align="right"
                    type="date"
                    placeholder="选择日期"
                    style="width: 280px;" :disabled="type === 'detail'">
                  </el-date-picker>
                </el-form-item>
              </el-col>
            </el-row>
            <el-row :gutter="24">
              <el-col :span="24">
                <el-form-item label="公司简介" prop="flowDesc">
                  <el-input type="textarea" :autosize="{ minRows: 2, maxRows: 4}" placeholder="请输入公司简介" v-model="form.capitalIntroduction" :disabled="type === 'detail'"/>
                </el-form-item>
              </el-col>
            </el-row>
          </el-form>
        </el-collapse-item>
        <el-collapse-item title="合同列表" name="2">
          <div class="head-container">
            <el-button 
              class="filter-item"
              size="mini"
              type="primary"
              icon="el-icon-plus"
              @click.native="addContract()" 
              :disabled="type === 'detail'"
            >
              上传
            </el-button>
            <el-button 
              class="filter-item"
              size="mini"
              type="warning"
              icon="el-icon-download"
              @click.native="downloadContract" 
              :disabled="type === 'detail'"
            >
              批量下载
            </el-button>
            <!--表单组件-->
            <el-dialog :close-on-click-modal="false" :show-close="false" :visible.sync="isOpen" title="新增合同" width="1000px">
              <el-form ref="contractForm" :model="contractForm" :rules="rules" size="small" label-width="120px"">
                <el-row :gutter="24">
                  <el-col :span="12">
                    <el-form-item label="合同编码" prop="contractCode">
                      <el-input v-model="contractForm.contractCode" style="width: 280px;" />
                    </el-form-item>
                  </el-col>
                  <el-col :span="12">
                    <el-form-item label="合同名称" prop="contractFileName">
                      <el-input v-model="contractForm.contractFileName" style="width: 280px;" />
                    </el-form-item>
                  </el-col>
                </el-row>
                <el-row :gutter="24">
                  <el-col :span="12">
                    <el-form-item label="甲方" prop="partyA">
                      <el-input v-model="contractForm.partyA" style="width: 280px;" />
                    </el-form-item>
                  </el-col>
                  <el-col :span="12">
                    <el-form-item label="乙方" prop="partyB">
                      <el-input v-model="contractForm.partyB" style="width: 280px;" />
                    </el-form-item>
                  </el-col>
                </el-row>
                <el-row :gutter="24">
                  <el-col :span="12">
                    <el-form-item label="合同开始日期" prop="contractCode">
                      <el-date-picker
                        value-format = "yyyy-MM-dd"
                        v-model="contractForm.contractStartTime"
                        align="right"
                        type="date"
                        placeholder="选择日期"
                        style="width: 280px;">
                      </el-date-picker>
                    </el-form-item>
                  </el-col>
                  <el-col :span="12">
                    <el-form-item label="合同结束日期" prop="contractCode">
                      <el-date-picker
                        value-format = "yyyy-MM-dd"
                        v-model="contractForm.contractEndTime"
                        align="right"
                        type="date"
                        placeholder="选择日期"
                        style="width: 280px;">
                      </el-date-picker>
                    </el-form-item>
                  </el-col>
                </el-row>
                <el-row :gutter="24">
                  <el-col :span="22">
                    <el-form-item label="合同描述" prop="contractDescription">
                      <el-input type="textarea" :autosize="{ minRows: 2, maxRows: 4}" placeholder="请输入合同描述" v-model="contractForm.contractDescription" />
                    </el-form-item>
                  </el-col>
                </el-row>
                <el-row :gutter="24">
                  <el-col :span="16">
                    <el-form-item label="上传合同" prop="contractDescription">
                      <input type="file" @change="uploadFile" accept=".pdf,.PDF"/>
                    </el-form-item>
                  </el-col>
                </el-row>
              </el-form>
              <div slot="footer" class="dialog-footer">
                <el-button @click="cancel">取 消</el-button>
                <el-button type="primary" @click="submit">确 认</el-button>
              </div>
            </el-dialog>
            <!--表格渲染-->
            <el-table ref="table" border="border" v-loading="loading" :data="data" size="small" style="width: 100%;" @selection-change="handleSelectionChange">
              <el-table-column type="selection" width="55" />
              <el-table-column prop="contractCode" label="合同编码" />
              <el-table-column prop="contractFileName" label="合同名称" />
              <el-table-column prop="contractDescription" label="合同描述" />
              <el-table-column prop="partyA" label="甲方" />
              <el-table-column prop="partyB" label="乙方" />
            </el-table>
            <!--分页组件-->
            <Pagination :pageNum="query.pageNum" :total="query.total" :pageSize="query.pageSize"
              @sizeChange="sizeChange" @currentChange="currentChange" />
          </div>
        </el-collapse-item>
      </el-collapse>
        <div style="text-align: center; margin-top: 30px;">
          <el-button @click="goBack">返 回</el-button>
          <el-button type="primary" @click="editAsset" :disabled="type === 'detail'">保 存</el-button>
        </div>
    </div>
</template>

<script>
import { getDetail, updateAsset } from '@/api/projectManage/fund'
import { upload, addContract, getList, downContract } from '@/api/projectManage/contract'

export default {
  name: 'FundDtl',
  data() {
    return {
      // 查询参数
      query: {
        pageNum: 1,
        pageSize: 10,
        total: 0
      },
      // 主列表传入id
      id: undefined,
      type: undefined,
      bankChannel: undefined,
      // 折叠开关
      activeNames: ['1', '2'],
      // 合同上传弹框开关
      isOpen: false,
      // 上传文件列表
      fileList: [],
      // 修改表单数据
      form: {},
      // 合同弹窗数据
      contractForm: {},
      // 主列表数据
      data: [],
      // 文件地址
      adder: '',
      // 选择行
      selectedRows: [],
      // 加载效果
      loading: false,
      // 按钮权限
      permission: {
        add: ['admin', 'projectContract:add'],
        edit: ['admin', 'projectContract:edit'],
        del: ['admin', 'projectContract:del']
      },
      rules: {
        bankChannel: [
          { required: true, message: '资金方编码不能为空', trigger: 'blur' }
        ],
        capitalNameShort: [
          { required: true, message: '资金方简称不能为空', trigger: 'blur' }
        ],
        capitalName: [
          { required: true, message: '资金方公司名称不能为空', trigger: 'blur' }
        ],
        mainProd: [
          { required: true, message: '主营产品不能为空', trigger: 'blur' }
        ],
        contractCode: [
          { required: true, message: '资金方接入日期不能为空', trigger: 'blur' }
        ]
      }    
    }
  },

  // // 钩子函数
  // created() {
  //   // 获取列表传参
  //   this.id = this.$route.query.id;
  //   this.type = this.$route.query.type;
  //   // 查询详情
  //   this.queryDetail({id: this.id});
  //   // 查询列表
  //   this.queryList(this.query);
  // },

  activated() {
    // 路由更新时执行
    // 获取列表传参
    this.id = this.$route.query.id;
    this.type = this.$route.query.type;
    this.bankChannel = this.$route.query.bankChannel;
    // 查询详情
    this.queryDetail({id: this.id});
    // 查询列表
    this.queryList(this.query);
  },

  methods: {

    // 查询资产方详情
    queryDetail(params) {
      getDetail(params).then((res) => {
        if(res.code === '000000') {
          this.form = res.data;
        };
      })
    },

    // 查询合同主列表
    queryList(params) {
      this.loading = true;
      params.contractOwner = this.bankChannel;
      getList(params).then((res) => {
        if(res.code === '000000') {
          this.data = res.data.content;
          this.query.total = res.data.totalElements;
        };
        this.loading = false;
      })
    },

    // 修改资产方
    editAsset() {
      updateAsset(this.form).then((res) => {
        if(res.code === '000000') {
          this.goBack();
          this.$message({
            message: '资产方修改成功!',
            type: 'success'
          });
        } else {
          this.$message({
            message: '资产方修改失败!',
            type: 'error'
          });
        }
      })
    },

    // 点击页码及上一页下一页按钮操作
    currentChange(val) {
      this.query.pageNum = val;
      this.queryList(this.query); //调用接口方法
    },

    //每页展示几条按钮操作
    sizeChange(val) {
      this.query.pageSize = val;
      this.queryList(this.query); //调用接口方法
    },

    addContract() {
      this.isOpen = true;
    },

    cancel() {
      this.isOpen = false;
      this.contractForm = {};
    },

    uploadFile(e) {
      let file = e.target.files[0];
      let fordata = new FormData();
      fordata.append('file', file);
      upload(fordata).then((res) => {
        if(res.code === '000000') {
          this.adder = res.data;
          this.$message({
            message: '合同上传成功!',
            type: 'success'
          });
        } else {
          this.$message({
            message: '合同上传失败!',
            type: 'error'
          });
        }
      })
    },

    // 新增合同
    submit() {
      this.contractForm.contractOwner = this.bankChannel;
      this.contractForm.ext1 = this.adder;;
      this.contractForm.extension = '.pdf';
      addContract(this.contractForm).then((res) => {
        if(res.code === '000000') {
          this.isOpen = false;
          this.contractForm = {};
          this.queryList(this.query);
          this.queryDetail({id: this.id});
          this.form = {};
          this.$message({
            message: '合同新增成功!',
            type: 'success'
          });
        } else {
          this.$message({
            message: '合同新增失败!',
            type: 'error'
          });
        }
      })
    },

    // 下载合同
    downloadContract() {
      if (Object.keys(this.selectedRows).length !== 0) {
        let urls = this.selectedRows.map(item => item.ext1);
        downContract(urls).then((res) => {
          if(res.code === '000000') {
            res.data.forEach(url => {
              window.location.href = url;
            });
          };
        })
      } else {
        this.$message({
          message: '请选择合同!',
          type: 'warning'
        });
      }
    },

    // 选择数据
    handleSelectionChange(val) {
      this.selectedRows = val;
    },

    goBack() {
      this.$router.back();
    }
  }
}
</script>

<style scoped>
::v-deep .el-collapse-item__header{
  font-size: 18px;
  font-weight: 600;
}
</style>
