<template>
  <div class="app-container">
    <div class="head-container">
      <!-- 搜索 -->
      <el-form :model="query" ref="queryForm" :inline="true">
        <el-form-item>
          <label class="el-form-item-label">资金方编码</label>
          <el-input v-model="query.bankChannel" clearable placeholder="请输入资金方编码" style="width: 185px;" class="filter-item" />
        </el-form-item>
          <label class="el-form-item-label">资金方名称</label>
          <el-input v-model="query.capitalName" clearable placeholder="请输入资金方公司名称" style="width: 185px;" class="filter-item" />
        <el-form-item>
          <label class="el-form-item-label">状态</label>
          <el-select v-model="query.enabled" clearable size="small" placeholder="请选择" class="filter-item" style="width: 90px" >
            <el-option v-for="item in ableStatusExt" :key="item.label" :label="item.value" :value="item.label" />
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button round size="mini" type="primary" icon="el-icon-search" @click="search">搜索</el-button>
          <el-button round size="mini" icon="el-icon-refresh-left" @click="reset">清空</el-button>
        </el-form-item>
      </el-form>
      <!--列表按钮-->
      <el-button
        v-permission="permission.add"
        class="filter-item"
        size="mini"
        type="primary"
        icon="el-icon-plus"
        @click="openDialog"
      >
        新增
      </el-button>
      <el-button
        v-permission="permission.edit"
        class="filter-item"
        size="mini"
        type="success"
        icon="el-icon-edit"
        @click="jumpPage('edit')"
      >
        修改
      </el-button>
      <el-button
        v-permission="permission.dtl"
        class="filter-item"
        size="mini"
        type="info"
        icon="el-icon-document"
        @click="jumpPage('detail')"
      >
        查看
      </el-button>
      <el-button
        v-permission="permission.dtl"
        class="filter-item"
        size="mini"
        type="danger"
        icon="el-icon-circle-close"
        @click="stopUse"
      >
        启用/停用
      </el-button>
      <!--表单组件-->
      <el-dialog :visible.sync="dialogVisible" :close-on-click-modal="false" title="新增资金方" width="580px" :show-close="false">
        <el-form ref="form" :model="form" :rules="rules" size="small" label-width="120px">
          <el-form-item label="资产金编码" prop="bankChannel">
            <el-input v-model="form.bankChannel" style="width: 370px;" clearable placeholder="请输入资金方编码" />
          </el-form-item>
          <el-form-item label="资产金简称" prop="capitalNameShort">
            <el-input v-model="form.capitalNameShort" style="width: 370px;" clearable placeholder="请输入资金方简称" />
          </el-form-item>
          <el-form-item label="资产金公司名称" prop="capitalName">
            <el-input v-model="form.capitalName" style="width: 370px;" clearable placeholder="请输入资金方公司名称" />
          </el-form-item>
        </el-form>
        <span slot="footer" class="dialog-footer">
          <el-button @click="closeDialog">取 消</el-button>
          <el-button type="primary" @click="saveAsset">保 存</el-button>
        </span>
      </el-dialog>
      <!--表格渲染-->
      <el-table ref="table" border="border" :data="data" size="small" style="width: 100%;" @selection-change="handleSelectionChange"  v-loading="loading">
        <el-table-column type="selection" width="55" />
        <el-table-column type="index" label="序号" width="55" />
        <el-table-column prop="bankChannel" label="资金方编码" />
        <el-table-column prop="capitalName" label="资金方简称" />
        <el-table-column prop="enabled" label="资金方状态" :formatter = "(row, column, cellValue) => {return ableStatusExt.find(item => item.label === cellValue).value}" />
        <el-table-column prop="capitalName" label="资金方公司名称" />
        <el-table-column prop="mainProd" label="主营产品" />
        <el-table-column prop="contactPerson" label="联系人" />
        <el-table-column prop="contactPhone" label="联系方式" />
        <el-table-column prop="capitalIntroduction" label="公司简介" />
      </el-table>
      <!--分页组件-->
      <Pagination :pageNum="query.pageNum" :total="query.total" :pageSize="query.pageSize"
        @sizeChange="sizeChange" @currentChange="currentChange" />
    </div>
  </div>
</template>

<script>
import { getList, addAsset, updateStatus } from '@/api/projectManage/fund'
import { get as getDictByName } from "@/api/system/dictDetail"

export default {
  name: 'FundList',
  data() {
    return {
      // 查询参数
      query: {
        pageNum: 1,
        pageSize: 10,
        total: 0
      },
      // 字典
      ableStatusExt: [],
      // 选取数据
      selectedRows: {},
      // 弹窗开关
      dialogVisible: false,
      // 新增表单数据
      form: {},
      // 主列表数据
      data: [],
      // 加载效果
      loading: false,
      // 按钮权限
      permission: {
        add: ['admin', 'flowConfig:add'],
        edit: ['admin', 'flowConfig:edit'],
        dtl: ['admin', 'flowConfig:dtl'],
        use: ['admin', 'flowConfig:use']
      },
      // 校验规则
      rules: {
        bankChannel: [
          { required: true, message: '资金方编码不能为空', trigger: 'blur' }
        ],
        capitalNameShort: [
          { required: true, message: '资金方简称不能为空', trigger: 'blur' }
        ],
        capitalName: [
          { required: true, message: '资金方公司名称不能为空', trigger: 'blur' }
        ]
      }
    }
  },

  // 钩子函数
  created() {
    // 获取资产方状态字典
    getDictByName("ableStatusExt").then(res => {
      this.ableStatusExt = res.content;
    });
    this.queryList(this.query)
  },

  methods: {

    // 搜索
    search() {
      this.queryList(this.query)
    },

    // 查询资产方主列表
    queryList(params) {
      this.loading = true;
      getList(params).then((res) => {
        if(res.code === '000000') {
          this.data = res.data.content;
          this.query.total = res.data.totalElements;
        };
        this.loading = false;
      })
    },

    // 新增资产方
    saveAsset() {
      addAsset(this.form).then((res) => {
        if(res.code === '000000') {
          this.dialogVisible = false;
          this.queryList(this.query);
          this.form = {};
          this.$message({
            message: '资金方新增成功!',
            type: 'success'
          });
        } else {
          this.$message({
            message: '资金方新增失败!',
            type: 'error'
          });
        }
      })
    },

    // 启用/停用
    stopUse() {
      if (Object.keys(this.selectedRows).length !== 0) {
        this.$confirm("确定启用/停用当前资金方？","警告",{type:"warning"}).then(()=>{
          const params = {
            id: this.selectedRows.id,
            enabled: this.selectedRows.enabled === 'INIT' ? 'ENABLE' : this.selectedRows.enabled === 'ENABLE' ? 'DISABLE' : 'ENABLE'
          }
          updateStatus(params).then((res) => {
            if(res.code === '000000') {
              this.queryList(this.query);
              this.$message({
                message: '资金方启用/停用成功!',
                type: 'success'
              });
            } else {
              this.$message({
                message: '资金方启用/停用失败!',
                type: 'error'
              });
            }
          })
        }).catch(()=>{

        });
      } else {
        this.$message({
          message: '请选择一个资金方!',
          type: 'warning'
        });
      }
    },

    // 跳转详情页
    jumpPage(type) {
      if (Object.keys(this.selectedRows).length !== 0) {
        this.$router.push({ name: 'FundDtl',   query: { id: this.selectedRows.id, type: type, bankChannel: this.selectedRows.bankChannel } });
      } else {
        this.$message({
          message: '请选择一个资金方!',
          type: 'warning'
        });
      }
    },

    // 打开新增弹窗
    openDialog() {
      this.dialogVisible = true;
    },

    // 点击页码及上一页下一页按钮操作
    currentChange(val) {
      this.query.pageNum = val;
      this.queryList(this.query); //调用接口方法
    },

    //每页展示几条按钮操作
    sizeChange(val) {
      this.query.pageSize = val;
      this.queryList(this.query); //调用接口方法
    },

    // 关闭新增弹窗
    closeDialog() {
      this.dialogVisible = false;
      this.form = {};
    },

    // 重置搜索条件
    reset() {
      this.query = {
        pageNum: 1,
        pageSize: 10,
        total: 0
      }
      this.queryList(this.query)
    },

    // 控制单选
    handleSelectionChange(val) {
      if (val.length > 1) {
        this.$refs.table.clearSelection();
        this.$refs.table.toggleRowSelection(val[val.length - 1]);
      }
      this.selectedRows = val[val.length - 1];
      if(!this.selectedRows) {
        this.selectedRows = {}
      }
    }
  }
}
</script>

<style scoped>
::v-deep .el-table__header-wrapper .el-checkbox {
  display: none;
}
</style>
